#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# SurveySparrow K8s Manifests - Commit Message Hook
echo "📝 Validating commit message..."

# Run commitlint
npx --no-install commitlint --edit "$1"

# Additional custom validation for SurveySparrow format
commit_msg=$(cat "$1")

echo "🔍 Running custom commit message validations..."

# Check for ticket ID format (optional but recommended)
if echo "$commit_msg" | grep -qE "^(feat|fix|security|config)"; then
  if ! echo "$commit_msg" | grep -qE "[A-Z]+-[0-9]+"; then
    echo "💡 Tip: Consider including a ticket ID (e.g., PROJ-123) for this commit type"
  fi
fi

# Validate commit message length
if [ ${#commit_msg} -gt 100 ]; then
  echo "⚠️  Warning: Commit message is longer than 100 characters"
fi

# Check for common typos in K8s context
if echo "$commit_msg" | grep -qiE "(kubernete[^s]|kuberentes|k8|yml)"; then
  echo "⚠️  Warning: Possible typo detected in commit message"
  echo "   - 'kubernetes' not 'kubernete'"
  echo "   - 'yaml' not 'yml'"
  echo "   - 'k8s' not 'k8'"
fi

echo "✅ Commit message validation completed!"
