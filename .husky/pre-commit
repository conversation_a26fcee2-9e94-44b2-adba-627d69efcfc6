#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# SurveySparrow K8s Manifests - Pre-commit Hook
echo "🔍 Running pre-commit hooks for K8s manifests..."

# Run pre-commit hooks
npx pre-commit run --hook-stage pre-commit

# Additional custom validations for K8s manifests
echo "🔧 Running custom K8s validations..."

# Check for common Kubernetes manifest issues
echo "📋 Checking for common K8s manifest issues..."

# Validate that all deployments have resource limits
find . -name "*.yaml" -o -name "*.yml" | grep -E "(deployment|statefulset)" | while read -r file; do
  if grep -q "kind: Deployment\|kind: StatefulSet" "$file"; then
    if ! grep -q "resources:" "$file"; then
      echo "⚠️  Warning: $file missing resource limits"
    fi
  fi
done

# Check for hardcoded secrets or sensitive data
echo "🔐 Checking for potential secrets..."
if grep -r -E "(password|secret|key|token)" --include="*.yaml" --include="*.yml" . | grep -v "name:" | grep -v "secretName:" | grep -v "# " | head -5; then
  echo "⚠️  Warning: Potential hardcoded secrets found. Please review."
fi

# Validate ArgoCD applications if argocd CLI is available
if command -v argocd >/dev/null 2>&1; then
  echo "🔄 Validating ArgoCD applications..."
  find . -path "*/argocd/*.yaml" -o -path "*/argocd/*.yml" | while read -r file; do
    echo "Validating: $file"
    argocd app validate "$file" || echo "❌ ArgoCD validation failed for $file"
  done
else
  echo "ℹ️  ArgoCD CLI not found, skipping ArgoCD validation"
fi

echo "✅ Pre-commit hooks completed!"
