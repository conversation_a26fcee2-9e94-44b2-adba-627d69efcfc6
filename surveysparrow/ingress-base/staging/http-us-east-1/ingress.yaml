apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=1200,deletion_protection.enabled=true,access_logs.s3.enabled=true,access_logs.s3.bucket=ss-squad-alb-logs,access_logs.s3.prefix=squad-alb
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-port: traffic-port
    alb.ingress.kubernetes.io/healthcheck-path: /haproxy/monitor
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '60'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '50'
    alb.ingress.kubernetes.io/success-codes: '200'
    alb.ingress.kubernetes.io/healthy-threshold-count: '2'
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '5'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:713859105457:certificate/c1008e87-b3fd-44c4-b749-27bf1efcb5b0, arn:aws:acm:us-east-1:713859105457:certificate/10499d97-8f9c-43a7-a175-57e6c9499913
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    alb.ingress.kubernetes.io/security-groups: sg-086a204e3eb0f3459
    alb.ingress.kubernetes.io/tags: CreatedBy=<EMAIL>,Team=Shared,Service=App-v1
    alb.ingress.kubernetes.io/subnets: subnet-0420050656953dce8, subnet-02e4af8986529e812, subnet-0177bb9a9ec89690c, subnet-0d989b0dab2eb4dee
spec:
  ingressClassName: my-aws-ingress-class
  rules: []