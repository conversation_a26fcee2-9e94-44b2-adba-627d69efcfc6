apiVersion: apps/v1
kind: Deployment
metadata:
  name: reputation-backend-worker
  labels:
    app: reputation-backend-worker
    application: reputation-backend
    group: reputation-backend-worker
spec:
  selector:
    matchLabels:
      app: reputation-backend-worker
      application: reputation-backend
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: reputation-backend-worker
        application: reputation-backend
        group: reputation-backend-worker
    spec:
      nodeSelector: 
        app: reputation-backend-worker
      containers:
      - name: reputation-backend-worker
        image: ss-reputation-backend-worker:latest
        imagePullPolicy: Always
        readinessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 30
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 40
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        volumeMounts:
        - name: config-mount
          mountPath: /app/config/production.json
          subPath: production.json
        ports:
          - containerPort: 8080
        resources:
          requests:
            cpu: 1000m
            memory: 1Gi
          limits:
            memory: 2Gi
      volumes:
      - name: config-mount
        configMap:
          name: reputation-backend-config
          items:
          - key: production.json
            path: production.json
