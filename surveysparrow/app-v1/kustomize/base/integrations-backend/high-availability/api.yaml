apiVersion: apps/v1
kind: Deployment
metadata:
  name: integrations-backend-api
  labels:
    app: integrations-backend-api
    application: integrations-backend
    group: integrations-backend-application
spec:
  selector:
    matchLabels:
      app: integrations-backend-api
      application: integrations-backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: integrations-backend-api
        application: integrations-backend
        group: integrations-backend-application
    spec:
      nodeSelector:
        app: integrations-backend-api
      containers:
      - name: integrations-backend-api
        image: ss-integrations-backend-application:latest
        imagePullPolicy: Always
        volumeMounts:
        - name: config-mount
          mountPath: /app/config/production.json
          subPath: production.json
        env:
        - name: NODE_CONFIG
          value: '{"worker":false}'
        readinessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 30
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 40
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        ports:
          - containerPort: 8080
        resources:
          requests:
            cpu: 1500m
            memory: 4Gi
          limits:
            memory: 4Gi
      volumes:
      - name: config-mount
        configMap:
          name: integrations-backend-config
          items:
          - key: production.json
            path: production.json
