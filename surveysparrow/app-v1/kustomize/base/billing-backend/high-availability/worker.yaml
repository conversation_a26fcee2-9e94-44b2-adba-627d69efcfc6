apiVersion: apps/v1
kind: Deployment
metadata:
  name: billing-backend-worker
  labels:
    app: billing-backend-worker
    group: billing-backend-worker
spec:
  selector:
    matchLabels:
      app: billing-backend-worker
      application: billing-backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: billing-backend-worker
        application: billing-backend
        group: billing-backend-worker
    spec:
      nodeSelector:
        app: billing-backend-worker
      containers:
      - name: billing-backend-worker
        image: ss-billing-backend-worker:latest
        imagePullPolicy: Always
        volumeMounts:
        - name: config-mount
          mountPath: /app/config/production.json
          subPath: production.json
        readinessProbe:
          httpGet:
            path: /status
            port: http
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 30
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /status
            port: http
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 40
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        ports:
        - containerPort: 8080
          name: http
        resources:
          requests:
            cpu: 1000m
            memory: 2Gi
          limits:
            memory: 4Gi
      volumes:
      - name: config-mount
        configMap:
          name: billing-backend-config
          items:
          - key: production.json
            path: production.json
