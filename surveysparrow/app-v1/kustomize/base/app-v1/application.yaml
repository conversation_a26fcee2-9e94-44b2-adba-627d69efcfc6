apiVersion: apps/v1
kind: Deployment
metadata:
  name: application
  labels:
    app: application
    group: app-v1-application
spec:
  selector:
    matchLabels:
      app: application
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 40%
      maxSurge: 80%
  template:
    metadata:
      labels:
        app: application
        application: application
        group: app-v1-application
    spec:
      containers:
      - name: application
        image: application:latest
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - name: config-mount
          mountPath: /app/config/staging.json
          subPath: staging.json
        env:
        - name: NODE_CONFIG
          value: '{"worker":false, "cronWorker":false,"layer":"app"}'
        readinessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 30
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 40
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        ports:
          - containerPort: 8080
        resources:
          requests:
            cpu: 1000m
            memory: 2Gi
          limits:
            memory: 2Gi
      volumes:
      - name: config-mount
        configMap:
          name: config
          items:
          - key: staging.json
            path: staging.json
