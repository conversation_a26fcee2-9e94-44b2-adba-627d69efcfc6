apiVersion: apps/v1
kind: Deployment
metadata:
  name: eui-backend-application
  labels:
    app: eui-backend-application
    application: eui-backend
    group: eui-backend-application
spec:
  selector:
    matchLabels:
      app: eui-backend-application
      application: eui-backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: eui-backend-application
        application: eui-backend
        group: eui-backend-application
    spec:
      serviceAccountName: eui-backend-sa
      nodeSelector:
        app: backend-application
      containers:
      - name: backend-application
        image: ss-eui-backend-application:latest
        imagePullPolicy: Always
        volumeMounts:
        - name: config-mount
          mountPath: /app/config/staging.json
          subPath: staging.json
        readinessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 30
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 40
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        ports:
          - containerPort: 8080
        resources:
          requests:
            cpu: 1000m
            memory: 512Mi
          limits:
            memory: 1Gi
      volumes:
      - name: config-mount
        configMap:
          name: eui-backend-config
          items:
          - key: staging.json
            path: staging.json
