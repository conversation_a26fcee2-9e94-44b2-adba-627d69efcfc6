apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - ../../../../base/haproxy
  - ../../../../../../ingress-base/staging/http-us-east-1
  - *****************:surveysparrow/surveysparrow-staging-config.git//helpsparrow/haproxy?ref=master
  

commonLabels:
  app: haproxy
  application: helpsparrow-haproxy

images:
  - name: haproxy
    newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/docker-hub/library/haproxy
    newTag: "2.6.17"

patches:
  - path: ingress.yaml
    target:
      kind: Ingress
      name: ingress
  - path: node_selector.yaml
    target:
      kind: Deployment
      name: haproxy
