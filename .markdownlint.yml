# Markdownlint configuration for SurveySparrow K8s manifests repository
# See https://github.com/<PERSON><PERSON>nson/markdownlint/blob/main/doc/Rules.md

# Default state for all rules
default: true

# Rule customizations for documentation
MD001: true  # Heading levels should only increment by one level at a time
MD003:       # Heading style
  style: "atx"
MD004:       # Unordered list style
  style: "dash"
MD005: true  # Inconsistent indentation for list items at the same level
MD007:       # Unordered list indentation
  indent: 2
MD009:       # Trailing spaces
  br_spaces: 2
MD010: true  # Hard tabs
MD011: true  # Reversed link syntax
MD012:       # Multiple consecutive blank lines
  maximum: 2
MD013:       # Line length
  line_length: 120
  heading_line_length: 120
  code_block_line_length: 120
  tables: false
  headings: false
  headers: false
MD014: true  # Dollar signs used before commands without showing output
MD018: true  # No space after hash on atx style heading
MD019: true  # Multiple spaces after hash on atx style heading
MD020: true  # No space inside hashes on closed atx style heading
MD021: true  # Multiple spaces inside hashes on closed atx style heading
MD022: true  # Headings should be surrounded by blank lines
MD023: true  # Headings must start at the beginning of the line
MD024:       # Multiple headings with the same content
  allow_different_nesting: true
  siblings_only: true
MD025:       # Multiple top level headings in the same document
  front_matter_title: "^\\s*title\\s*[:=]"
MD026:       # Trailing punctuation in heading
  punctuation: ".,;:!?"
MD027: true  # Multiple spaces after blockquote symbol
MD028: true  # Blank line inside blockquote
MD029:       # Ordered list item prefix
  style: "ordered"
MD030:       # Spaces after list markers
  ul_single: 1
  ol_single: 1
  ul_multi: 1
  ol_multi: 1
MD031: true  # Fenced code blocks should be surrounded by blank lines
MD032: true  # Lists should be surrounded by blank lines
MD033:       # Inline HTML
  allowed_elements: ["br", "sub", "sup", "kbd", "details", "summary"]
MD034: true  # Bare URL used
MD035: true  # Horizontal rule style
MD036: true  # Emphasis used instead of a heading
MD037: true  # Spaces inside emphasis markers
MD038: true  # Spaces inside code span elements
MD039: true  # Spaces inside link text
MD040: true  # Fenced code blocks should have a language specified
MD041: true  # First line in file should be a top level heading
MD042: true  # No empty links
MD043: false # Required heading structure (disabled for flexibility)
MD044:       # Proper names should have the correct capitalization
  names: ["SurveySparrow", "Kubernetes", "ArgoCD", "Kustomize", "YAML", "JSON", "Docker", "AWS", "ECR"]
  code_blocks: false
MD045: true  # Images should have alternate text (alt text)
MD046:       # Code block style
  style: "fenced"
MD047: true  # Files should end with a single newline character
MD048:       # Code fence style
  style: "backtick"
MD049:       # Emphasis style
  style: "asterisk"
MD050:       # Strong style
  style: "asterisk"
MD051: true  # Link fragments should be valid
MD052: true  # Reference links and images should use a label that is defined
MD053: true  # Link and image reference definitions should be needed
