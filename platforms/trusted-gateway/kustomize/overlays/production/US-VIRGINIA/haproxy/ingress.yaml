apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/load-balancer-name: us-vi-trusted-gateway-alb
    alb.ingress.kubernetes.io/healthcheck-path: /haproxy/monitor
    alb.ingress.kubernetes.io/target-node-labels: app=trusted-gateway
    alb.ingress.kubernetes.io/group.name: ss-internal-services
    alb.ingress.kubernetes.io/security-groups: sg-0f529b90079565b78
    alb.ingress.kubernetes.io/tags: CreatedBy=<EMAIL>,Team=Platforms,Service=TrustedGateway
spec:
  ingressClassName: my-aws-ingress-class
  rules:
    - host: "*.internal.surveysparrow.com"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: haproxy-service
                port:
                  number: 8080
    - host: "*.sparrowmailer.com"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: haproxy-service
                port:
                  number: 8080
