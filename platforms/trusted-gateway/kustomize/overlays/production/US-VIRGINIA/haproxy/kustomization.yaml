apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - ../../../../../../../common/haproxy/kustomize/base
  - ../../../../../../../common/haproxy/kustomize/base/high-availability
  - ../../../../../../ingress-base/production/http-us-virginia
  - *****************:surveysparrow/surveysparrow-production-config.git//platforms/trusted-gateway/US-VIRGINIA?ref=master

commonLabels:
  app: haproxy
  application: haproxy

images:
  - name: haproxy
    newName: 974682937630.dkr.ecr.us-east-1.amazonaws.com/docker-hub/library/haproxy
    newTag: "3.0"

patches:
  - path: ingress.yaml
  - path: node_selector.yaml
