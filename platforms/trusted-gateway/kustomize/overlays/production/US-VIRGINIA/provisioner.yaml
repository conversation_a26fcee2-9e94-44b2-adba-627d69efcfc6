apiVersion: karpenter.sh/v1beta1
kind: NodePool
metadata:
  name: np
spec:
  disruption:
    consolidationPolicy: WhenUnderutilized
  limits:
    cpu: "1000"
    memory: 1000Gi
  template:
    metadata:
      labels:
        app: trusted-gateway
        intent: app
    spec:
      nodeClassRef:
        name: us-virginia-trusted-gateway-nc
      taints:
      - key: app
        value: platforms-reserved
        effect: NoSchedule
      requirements:
      - key: karpenter.sh/capacity-type
        operator: In
        values:
        - on-demand
        - spot
      - key: node.kubernetes.io/instance-type
        operator: In
        values:
        - t4g.small
        - t4g.medium
      - key: kubernetes.io/arch
        operator: In
        values:
        - arm64
      - key: topology.kubernetes.io/zone
        operator: In
        values:
        - us-east-1a
        - us-east-1b
        - us-east-1c
        - us-east-1d
        - us-east-1f
      - key: kubernetes.io/os
        operator: In
        values:
        - linux
---
apiVersion: karpenter.k8s.aws/v1beta1
kind: EC2NodeClass
metadata:
  name: nc
spec:
  amiFamily: AL2
  role: KarpenterNodeRole-platform-production-cluster
  securityGroupSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  subnetSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  tags:
    CreatedBy: <EMAIL>
    IntentLabel: app
    KarpenterProvisionerName: trusted-gateway
    Service: TrustedGateway
    Team: Anchor
    app: trusted-gateway
    intent: app
    Name: Karpenter/TrustedGateway
