apiVersion: apps/v1
kind: Deployment
metadata:
  name: application
  labels:
    app: promptstack
    service: application
spec:
  revisionHistoryLimit: 5
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: promptstack
      service: application
  template:
    metadata:
      labels:
        app: promptstack
        service: application
      annotations:
        eks.amazonaws.com/role-arn: arn:aws:iam::************:role/PromptstackServiceRole
    spec:
      serviceAccountName: service-account
      securityContext:
        runAsUser: 100
        runAsGroup: 101
      containers:
      - name: promptstack
        image: application:latest
        imagePullPolicy: IfNotPresent
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          readOnlyRootFilesystem: true
          seccompProfile:
            type: RuntimeDefault
        volumeMounts:
        - name: config-mount
          mountPath: /app/config/production.json
          subPath: production.json
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - sleep 30
        resources:
          requests:
            cpu: 500m
            memory: 512Mi
          limits:
            memory: 800Mi
        env:
        - name: NODE_ENV
          value: production
        ports:
        - containerPort: 3000
          name: http-server
        readinessProbe:
          httpGet:
            path: /api/health
            port: http-server
          initialDelaySeconds: 10
          periodSeconds: 30
          successThreshold: 1
          failureThreshold: 4
        livenessProbe:
          httpGet:
            path: /api/health
            port: http-server
          initialDelaySeconds: 10
          periodSeconds: 30
          successThreshold: 1
          failureThreshold: 4
      nodeSelector:
        app: promptstack
      tolerations:
      - key: app
        operator: Equal
        value: platforms-reserved
        effect: NoSchedule
      terminationGracePeriodSeconds: 30
      volumes:
      - name: config-mount
        configMap:
          name: config
          items:
          - key: production.json
            path: production.json
