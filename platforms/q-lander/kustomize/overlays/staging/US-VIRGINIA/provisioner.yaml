apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: np
spec:
  template:
    metadata:
      labels:
        app: q-lander
      annotations:
        Name: q-lander
        NodePool: q-lander
        CreatedBy: <EMAIL>
    spec:
      nodeClassRef:
        name: us-virginia-q-lander-nc
        group: karpenter.k8s.aws
        kind: EC2NodeClass
      taints:
      - key: app
        value: platforms-reserved
        effect: NoSchedule
      requirements:
      - key: karpenter.sh/capacity-type
        operator: In
        values:
        - on-demand
      - key: node.kubernetes.io/instance-type
        operator: In
        values:
        - t4g.micro
        - t4g.small
      - key: topology.kubernetes.io/zone
        operator: In
        values:
        - us-east-1b
      - key: kubernetes.io/arch
        operator: In
        values:
        - arm64
      - key: kubernetes.io/os
        operator: In
        values: 
        - linux
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 720h
  limits:
    cpu: 30
    memory: 60Gi
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: nc
spec:
  amiFamily: AL2023
  amiSelectorTerms:
    - alias: al2023@latest
  metadataOptions:
    httpPutResponseHopLimit: 3
  subnetSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  securityGroupSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  role: KarpenterNodeRole-QLander
  tags:
    environment: staging
    KarpenterProvisionerName: q-lander
    CreatedBy: <EMAIL>
    Team: Hygiene
    Service: q-lander
    IntentLabel: app
    app: q-lander
    intent: app
    Name: Karpenter/q-lander
