apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /health
    alb.ingress.kubernetes.io/target-node-labels: app=q-lander
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=1200,
      deletion_protection.enabled=true,
      access_logs.s3.enabled=true,
      access_logs.s3.bucket=ss-squad-alb-logs,
      access_logs.s3.prefix=ss-microservices-alb
spec:
  rules:
    - host: sqs.salesparrow.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: service
                port:
                  number: 3000

