apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../base
- ../../../../../ingress-base/staging/http-us-virginia
- provisioner.yaml
- *****************:surveysparrow/surveysparrow-staging-config.git/platforms/q-lander/US-VIRGINIA?ref=preproduction-master

namePrefix: us-virginia-q-lander-

namespace: q-lander

images:
- name: application
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/q-lander/application
  newTag: master_c7bb1704

patches:
- path: ingress_patch.yaml
