apiVersion: karpenter.sh/v1beta1
kind: NodePool
metadata:
  name: np
spec:
  template:
    metadata:
      labels:
        app: q-lander
      annotations:
        Name: q-lander
        NodePool: q-lander
        CreatedBy: <EMAIL>
    spec:
      nodeClassRef:
        name: us-virginia-q-lander-nc
      taints:
        - key: app
          value: platforms-reserved
          effect: NoSchedule
      requirements:
        - key: karpenter.sh/capacity-type
          operator: In
          values:
            - on-demand
        - key: node.kubernetes.io/instance-type
          operator: In
          values:
            - t4g.micro
            - t4g.small
        - key: topology.kubernetes.io/zone
          operator: In
          values:
            - us-east-1a
        - key: kubernetes.io/arch
          operator: In
          values:
            - arm64
        - key: kubernetes.io/os
          operator: In
          values:
            - linux
  disruption:
    consolidationPolicy: WhenUnderutilized
    expireAfter: 720h
  limits:
    cpu: 100
    memory: 200Gi
---
apiVersion: karpenter.k8s.aws/v1beta1
kind: EC2NodeClass
metadata:
  name: nc
spec:
  amiFamily: AL2
  subnetSelectorTerms:
    - tags:
        PlatformsK8s: "true"
  securityGroupSelectorTerms:
    - tags:
        PlatformsK8s: "true"
  role: KarpenterNodeRole-platform-production-cluster
  tags:
    environment: production
    KarpenterProvisionerName: q-lander
    CreatedBy: <EMAIL>
    Team: Hygiene
    Service: QLander
    IntentLabel: app
    app: QLander
    intent: app
    Name: Karpenter/QLander
