apiVersion: apps/v1
kind: Deployment
metadata:
  name: application
  labels:
    app: q-lander
    service: q-lander
spec:
  revisionHistoryLimit: 5
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: q-lander
  template:
    metadata:
      labels:
        app: q-lander
      annotations:
        eks.amazonaws.com/role-arn: arn:aws:iam::************:role/QLanderServiceRole
    spec:
      serviceAccountName: service-account
      securityContext:
        runAsUser: 1001
        runAsGroup: 1001
      containers:
      - name: q-lander
        image: application:latest
        imagePullPolicy: Always
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          readOnlyRootFilesystem: true
          seccompProfile:
            type: RuntimeDefault
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - sleep 15
        resources:
          requests:
            cpu: 300m
            memory: 512Mi
          limits:
            memory: 512Mi
        env:
        - name: ENV
          value: staging
        ports:
        - containerPort: 3000
          name: http-server
        - containerPort: 6375
          name: redis
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 4
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 4
        volumeMounts:
        - name: config-json
          mountPath: /app/config.json
          subPath: config.json
      volumes:
      - name: config-json
        configMap:
          name: config
          defaultMode: 420
          items:
          - key: config.json
            path: config.json
      nodeSelector:
        app: q-lander
      tolerations:
      - key: app
        operator: Equal
        value: platforms-reserved
        effect: NoSchedule
      terminationGracePeriodSeconds: 15
