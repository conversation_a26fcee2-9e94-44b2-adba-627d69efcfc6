apiVersion: batch/v1
kind: Job
metadata:
  generateName: ${NAME_PREFIX}-sparrowpay-payments-migration-
  namespace: ${NAMESPACE}
  labels:
    migration/id: "${MIGRATION_ID}"
    migration/build: "${BUILD_NUMBER}"
    migration/type: ${MIGRATION_TYPE}
spec:
  template:
    metadata:
      labels:
        migration/id: "${MIGRATION_ID}"
        migration/build: "${BUILD_NUMBER}"
        migration/type: ${MIGRATION_TYPE}
    spec:
      containers:
      - name: sparrowpay-migration
        image: ${ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com/sparrowpay/payments:latest
        imagePullPolicy: Always
        command: ["sh", "-c"]
        args:
        - sleep 5 && node dist/src/migrations/index.js
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - sleep 30
        resources:
          requests:
            cpu: 1000m
            memory: 1024Mi
          limits:
            memory: 1024Mi
            cpu: 1000m
        envFrom:
        - configMapRef:
            name: ${NAME_PREFIX}-sparrowpay-payments-migration-config
        - secretRef:
            name: sparrowpay-payments-secrets
        env:
        - name: MI<PERSON>ATION_TYPE
          value: ${MIGRATION_TYPE}
        - name: NODE_ENV
          value: ${environment}
      restartPolicy: Never
      terminationGracePeriodSeconds: 30
      nodeSelector:
        app: sparrowpay-microservices
      tolerations:
      - key: app
        operator: Equal
        value: platforms-reserved
        effect: NoSchedule
  ttlSecondsAfterFinished: 300  # Cleanup after 5 minutes
  backoffLimit: 0