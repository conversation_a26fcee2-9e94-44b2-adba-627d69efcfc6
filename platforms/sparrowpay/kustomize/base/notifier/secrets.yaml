apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: secretprovider
spec:
  provider: aws
  parameters: 
    objects: |
      - objectName: sparrowpay/notifier/staging
        objectType: secretsmanager
        jmesPath:
          - path: DB_USERNAME
            objectAlias: dbUsernameAlias
          - path: DB_PASSWORD
            objectAlias: dbPasswordAlias
          - path: BOT_TOKEN
            objectAlias: botTokenAlias
          - path: DB_READ_PASSWORD
            objectAlias: dbReadPasswordAlias
  secretObjects:                
    - secretName: sparrowpay-notifier-secrets-envs
      type: Opaque
      data:
        - objectName: botTokenAlias
          key: BOT_TOKEN
        - objectName: dbUsernameAlias
          key: DB_USERNAME
        - objectName: dbPasswordAlias
          key: DB_PASSWORD
        - objectName: dbReadPasswordAlias
          key: DB_READ_PASSWORD
