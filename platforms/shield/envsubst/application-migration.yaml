apiVersion: batch/v1
kind: Job
metadata:
  generateName: ${NAME_PREFIX}-shield-application-migration-
  namespace: ${NAMESPACE}
  labels:
    migration/id: "${MIGRATION_ID}"
    migration/build: "${BUILD_NUMBER}"
    migration/type: ${MIGRATION_TYPE}
spec:
  template:
    metadata:
      labels:
        migration/id: "${MIGRATION_ID}"
        migration/build: "${BUILD_NUMBER}"
        migration/type: ${MIGRATION_TYPE}
      annotations:
        eks.amazonaws.com/role-arn: arn:aws:iam::${ACCOUNT_ID}:role/${IAM_ROLE_NAME}
    spec:
      serviceAccountName: ${NAME_PREFIX}-shield-service-account
      containers:
      - name: shield
        image: ${ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com/${BASE_IMAGE_NAME}/migration:latest
        command: ["sh", "-c"]
        args:
          - sleep 10 && ./migration pre
        imagePullPolicy: Always
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - sleep 30
        resources:
          requests:
            cpu: 600m
            memory: 256Mi
          limits:
            memory: 800Mi
        env:
        - name: SHIELD_ENV
          value: ${ENVIRONMENT}
        - name: MIGRATION_TYPE
          value: ${MIGRATION_TYPE}
        volumeMounts:
        - name: config-json
          mountPath: /etc/shield/${ENVIRONMENT}.json
          subPath: ${ENVIRONMENT}.json
      volumes:
      - name: config-json
        configMap:
          name: ${NAME_PREFIX}-shield-application-migration-config
          defaultMode: 420
      nodeSelector:
        app: shield
      tolerations:
      - key: app
        operator: Equal
        value: platforms-reserved
        effect: NoSchedule
      restartPolicy: Never
      terminationGracePeriodSeconds: 30
  ttlSecondsAfterFinished: 3600  # Cleanup after 1 hour
  backoffLimit: 0
