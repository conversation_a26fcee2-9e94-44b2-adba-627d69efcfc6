apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:713859105457:certificate/6f4375d8-8cb8-437e-8609-d89a1bc2219b
    alb.ingress.kubernetes.io/group.name: shield-ingress-group
    alb.ingress.kubernetes.io/load-balancer-name: microservice-alb
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=1200,deletion_protection.enabled=true,access_logs.s3.enabled=true,access_logs.s3.bucket=ss-squad-alb-logs,access_logs.s3.prefix=shield-grpc
    alb.ingress.kubernetes.io/target-node-labels: app=shield
    alb.ingress.kubernetes.io/tags: CreatedBy=<EMAIL>,Team=SRE,Service=Shield
spec:
  ingressClassName: alb
  rules:
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ssl-redirect
            port:
              name: use-annotation
  - host: shield.salesparrow.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: application-service
            port:
              number: 5051
