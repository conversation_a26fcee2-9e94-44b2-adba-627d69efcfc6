apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: np
spec:
  template:
    metadata:
      labels:
        app: shield
      annotations:
        Name: shield
        NodePool: shield
        CreatedBy: <EMAIL>
    spec:
      expireAfter: 720h
      nodeClassRef:
        name: us-virginia-shield-nc
        group: karpenter.k8s.aws
        kind: EC2NodeClass
      taints:
      - key: app
        value: platforms-reserved
        effect: NoSchedule
      requirements:
      - key: karpenter.sh/capacity-type
        operator: In
        values:
        - on-demand
      - key: node.kubernetes.io/instance-type
        operator: In
        values:
        - t4g.small
        - t4g.medium
        - t4g.large
      - key: topology.kubernetes.io/zone
        operator: In
        values:
        - us-east-1b
      - key: kubernetes.io/arch
        operator: In
        values:
        - arm64
      - key: kubernetes.io/os
        operator: In
        values:
        - linux
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
  limits:
    cpu: 100
    memory: 800Gi
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: nc
spec:
  amiFamily: AL2023
  amiSelectorTerms:
  - alias: al2023@latest
  metadataOptions:
    httpPutResponseHopLimit: 3
  subnetSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  securityGroupSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  role: KarpenterNodeRole-Shield
  tags:
    environment: staging
    KarpenterProvisionerName: Shield
    CreatedBy: <EMAIL>
    Team: SRE
    Service: Shield
    IntentLabel: app
    app: shield
    intent: app
    Name: Karpenter/shield
