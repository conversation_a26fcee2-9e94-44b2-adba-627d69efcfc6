apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-central-1:974682937630:certificate/721b678f-066c-4e62-8307-d3292df7696a
    alb.ingress.kubernetes.io/group.name: shield-ingress-eu-group
    alb.ingress.kubernetes.io/load-balancer-attributes: access_logs.s3.enabled=true,access_logs.s3.bucket=ss-eu-alb-logs,access_logs.s3.prefix=ff-shield,deletion_protection.enabled=true
    alb.ingress.kubernetes.io/load-balancer-name: shield-grpc-eu-alb
    alb.ingress.kubernetes.io/target-node-labels: app=shield
    alb.ingress.kubernetes.io/tags: CreatedBy=<EMAIL>,Team=SRE,Service=Shield
spec:
  ingressClassName: alb
  rules:
    - http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ssl-redirect
                port:
                  name: use-annotation
    - host: eu-shield.surveysparrow.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: application-service
                port:
                  number: 5051
