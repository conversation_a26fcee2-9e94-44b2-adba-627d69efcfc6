apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: np
  labels:
    app: shield
    intent: app
spec:
  template:
    metadata:
      labels:
        app: shield
        intent: app
    spec:
      requirements:
      - key: kubernetes.io/arch
        operator: In
        values:
        - arm64
      - key: kubernetes.io/os
        operator: In
        values: 
        - linux
      - key: karpenter.sh/capacity-type
        operator: In
        values: 
        - on-demand
      - key: node.kubernetes.io/instance-type
        operator: In
        values: 
        - t4g.small
        - t4g.medium
        - t4g.large
      - key: topology.kubernetes.io/zone
        operator: In
        values: 
        - eu-central-1a
        - eu-central-1b
        - eu-central-1c
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: eu-frankfurt-shield-nc
      taints:
      - key: app
        value: platforms-reserved
        effect: NoSchedule
      expireAfter: Never
  limits:
    cpu: 1000
    memory: 1000Gi
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 1m
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: nc
  labels:
    app: shield
    intent: app
spec:
  amiFamily: AL2023
  amiSelectorTerms:
  - alias: al2023@latest
  role: KarpenterNodeRole-Shield
  subnetSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  securityGroupSelectorTerms:
  - tags:
      PlatformsK8s: "true"
  tags:
    Service: Shield
    CreatedBy: <EMAIL>
    Team: SRE
    app: shield
    KarpenterProvisionerName: shield
    environment: production
    IntentLabel: app
    intent: app
    Name: Karpenter/shield
