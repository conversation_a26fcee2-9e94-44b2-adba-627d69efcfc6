apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:974682937630:certificate/34e0c1da-7a2f-4ea8-aa39-2cb69dfebe7a
    alb.ingress.kubernetes.io/group.name: shield-ingress-us-group
    alb.ingress.kubernetes.io/load-balancer-attributes: access_logs.s3.enabled=true,access_logs.s3.bucket=ss-us-alb-logs,access_logs.s3.prefix=vi-shield,deletion_protection.enabled=true
    alb.ingress.kubernetes.io/load-balancer-name: shield-grpc-us-alb
    alb.ingress.kubernetes.io/target-node-labels: app=shield
    alb.ingress.kubernetes.io/tags: CreatedBy=<EMAIL>,Team=SRE,Service=Shield
spec:
  ingressClassName: alb
  rules:
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ssl-redirect
            port:
              name: use-annotation
  - host: shield.surveysparrow.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: application-service
            port:
              number: 5051
