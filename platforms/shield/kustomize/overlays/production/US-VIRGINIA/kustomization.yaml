apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namePrefix: us-virginia-shield-

namespace: shield

resources:
- ../../../base
- ../../../base/high-availability
- ../../../../../ingress-base/production/grpc-us-virginia
- provisioner.yaml
- *****************:surveysparrow/surveysparrow-production-config.git/platforms/shield/US-VIRGINIA?ref=master

patches:
- path: hpa_replica_patch.yaml
- path: service_account_patch.yaml
- path: deployment_patch.yaml
- path: ingress_patch.yaml

images:
- name: application
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/shield/application
  newTag: production-application-04-04-2025-5_fd84f385
