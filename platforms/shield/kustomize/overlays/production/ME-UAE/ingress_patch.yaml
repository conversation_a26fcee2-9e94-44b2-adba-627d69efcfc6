apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:me-central-1:974682937630:certificate/661a31ac-6abd-40b2-b768-ef86136e779d
    alb.ingress.kubernetes.io/group.name: shield-ingress-me-uae-group
    alb.ingress.kubernetes.io/load-balancer-attributes: access_logs.s3.enabled=true,access_logs.s3.bucket=ss-me-alb-logs,access_logs.s3.prefix=uae-shield,deletion_protection.enabled=true
    alb.ingress.kubernetes.io/load-balancer-name: shield-grpc-me-alb
    alb.ingress.kubernetes.io/target-node-labels: app=shield
    alb.ingress.kubernetes.io/tags: CreatedBy=<EMAIL>,Team=SRE,Service=Shield
spec:
  ingressClassName: alb
  rules:
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ssl-redirect
            port:
              name: use-annotation
  - host: me-shield.surveysparrow.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: application-service
            port:
              number: 5051
