apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:ap-south-1:974682937630:certificate/a7e430dc-927a-4f91-a6c0-1aaf836e718d
    alb.ingress.kubernetes.io/group.name: shield-ingress-ap-group
    alb.ingress.kubernetes.io/load-balancer-attributes: access_logs.s3.enabled=true,access_logs.s3.bucket=ss-ap-alb-logs,access_logs.s3.prefix=mi-shield,deletion_protection.enabled=true
    alb.ingress.kubernetes.io/load-balancer-name: shield-grpc-ap-alb
    alb.ingress.kubernetes.io/target-node-labels: app=shield
    alb.ingress.kubernetes.io/tags: CreatedBy=<EMAIL>,Team=SRE,Service=Shield
spec:
  ingressClassName: alb
  rules:
    - http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ssl-redirect
                port:
                  name: use-annotation
    - host: ap-shield.surveysparrow.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: application-service
                port:
                  number: 5051