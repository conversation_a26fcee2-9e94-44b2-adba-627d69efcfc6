apiVersion: apps/v1
kind: Deployment
metadata:
  name: application
  labels:
    app: shield
spec:
  revisionHistoryLimit: 5
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: shield
  template:
    metadata:
      labels:
        app: shield
        application: shield
      annotations:
        eks.amazonaws.com/role-arn: arn:aws:iam::************:role/ShieldServiceRole
    spec:
      serviceAccountName: service-account
      containers:
      - name: shield
        image: application:latest
        imagePullPolicy: IfNotPresent
        lifecycle:
          preStop:
            exec:
              command: 
                - /bin/sh
                -  -c
                - sleep 30
        resources:
          requests:
            cpu: 500m
            memory: 512Mi
        env:
        - name: SHIELD_ENV
          value: staging
        ports:
        - containerPort: 5051
          name: grpc-server
        readinessProbe:
          grpc:
            # gRPC probes do not support named ports.
            port: 5051
          initialDelaySeconds: 15
          periodSeconds: 5
          successThreshold: 1
          failureThreshold: 2
        livenessProbe:
          grpc:
            # gRPC probes do not support named ports.
            port: 5051
          initialDelaySeconds: 15
          periodSeconds: 5
          successThreshold: 1
          failureThreshold: 2
        volumeMounts:
        - name: config-json
          mountPath: /etc/shield
      volumes:
      - name: config-json
        configMap:
          name: config
      nodeSelector:
        app: shield
      tolerations:
      - key: app
        operator: Equal
        value: platforms-reserved
        effect: NoSchedule
      terminationGracePeriodSeconds: 80