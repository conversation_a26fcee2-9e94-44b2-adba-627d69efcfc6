apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../base
- ../../../../../ingress-base/production/http-us-virginia
- provisioner.yaml
- pdb.yaml
- *****************:surveysparrow/surveysparrow-production-config.git/platforms/edith/US-VIRGINIA?ref=master

namePrefix: us-virginia-edith-

namespace: edith

images:
- name: admin-server
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/edith/admin-server
  newTag: production-admin-server-11-06-2025-2_3e1af7137
- name: api-server
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/edith/api-server
  newTag: production-api-server-11-06-2025-2_3e1af7135
- name: worker
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/edith/worker
  newTag: production-worker-11-06-2025-1_3e1af7136

patches:
- path: deployment_patch.yaml
  target:
    group: apps
    kind: Deployment
    labelSelector: service=edith
    version: v1
- path: api_resource_patch.yaml
- path: worker_resource_patch.yaml
- path: service_account_patch.yaml
- path: ingress_patch.yaml
- path: api_hpa_patch.yaml
