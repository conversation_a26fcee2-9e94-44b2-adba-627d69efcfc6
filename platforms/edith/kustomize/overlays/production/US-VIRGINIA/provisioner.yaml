apiVersion: karpenter.sh/v1beta1
kind: NodePool
metadata:
  name: np
spec:
  template:
    metadata:
      labels:
        app: edith
      annotations:
        Name: edith
        NodePool: edith
        CreatedBy: <EMAIL>
    spec:
      nodeClassRef:
        name: us-virginia-edith-nc
      taints:
        - key: app
          value: platforms-reserved
          effect: NoSchedule
      requirements:
        - key: karpenter.sh/capacity-type
          operator: In
          values:
            - on-demand
        - key: node.kubernetes.io/instance-type
          operator: In
          values:
            - t4g.small
            - t4g.medium
            - t4g.large
        - key: topology.kubernetes.io/zone
          operator: In
          values:
            - us-east-1a
            - us-east-1b
            - us-east-1c
            - us-east-1d
        - key: kubernetes.io/arch
          operator: In
          values:
            - arm64
        - key: kubernetes.io/os
          operator: In
          values:
            - linux
  disruption:
    consolidationPolicy: WhenUnderutilized
    expireAfter: 720h
  limits:
    cpu: 30
    memory: 60Gi
---
apiVersion: karpenter.k8s.aws/v1beta1
kind: EC2NodeClass
metadata:
  name: nc
spec:
  amiFamily: AL2
  subnetSelectorTerms:
    - tags:
        PlatformsK8s: "true"
  securityGroupSelectorTerms:
    - tags:
        PlatformsK8s: "true"
  role: KarpenterNodeRole-Edith
  tags:
    environment: production
    KarpenterProvisionerName: edith
    CreatedBy: <EMAIL>
    Team: Hygiene
    Service: Edith
    IntentLabel: app
    app: edith
    intent: app
    Name: Karpenter/edith
