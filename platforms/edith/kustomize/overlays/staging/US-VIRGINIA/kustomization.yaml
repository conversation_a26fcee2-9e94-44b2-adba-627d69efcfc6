apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../base
- ../../../../../ingress-base/staging/http-us-virginia
- mailpit-a
- mailpit-b
- provisioner.yaml
- pdb.yaml
- *****************:surveysparrow/surveysparrow-staging-config.git/platforms/edith/US-VIRGINIA?ref=preproduction-master

namePrefix: us-virginia-edith-

namespace: edith

images:
- name: admin-server
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/edith/admin-server
  newTag: preproduction-admin-server-29may2025-edith-8_3e1af71419
- name: api-server
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/edith/api-server
  newTag: preproduction-api-server-25jun2025-edith-4_d4f0eb7423
- name: worker
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/edith/worker
  newTag: preproduction-worker-25jun2025-edith-3_d4f0eb7424

patches:
- path: ingress.yaml
