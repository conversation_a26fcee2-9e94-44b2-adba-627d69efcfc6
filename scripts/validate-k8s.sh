#!/bin/bash

# SurveySparrow K8s Manifests Validation Script
# This script validates Kubernetes manifests, Kustomize configurations, and ArgoCD applications

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    log_info "Checking dependencies..."
    
    local missing_tools=()
    
    if ! command -v kubectl >/dev/null 2>&1; then
        missing_tools+=("kubectl")
    fi
    
    if ! command -v kustomize >/dev/null 2>&1; then
        missing_tools+=("kustomize")
    fi
    
    if ! command -v yamllint >/dev/null 2>&1; then
        missing_tools+=("yamllint")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_info "Please install the missing tools and try again."
        exit 1
    fi
    
    log_success "All dependencies are available"
}

# Validate YAML syntax
validate_yaml_syntax() {
    log_info "Validating YAML syntax..."
    
    local yaml_files
    yaml_files=$(find . -name "*.yaml" -o -name "*.yml" | grep -v ".git" | grep -v "node_modules")
    
    local failed_files=()
    
    while IFS= read -r file; do
        if ! yamllint "$file" >/dev/null 2>&1; then
            failed_files+=("$file")
            log_error "YAML syntax error in: $file"
        fi
    done <<< "$yaml_files"
    
    if [ ${#failed_files[@]} -eq 0 ]; then
        log_success "All YAML files have valid syntax"
        return 0
    else
        log_error "YAML validation failed for ${#failed_files[@]} files"
        return 1
    fi
}

# Validate Kubernetes manifests
validate_kubernetes_manifests() {
    log_info "Validating Kubernetes manifests..."
    
    local k8s_files
    k8s_files=$(find . -name "*.yaml" -o -name "*.yml" | grep -v ".git" | grep -v "node_modules" | grep -v "kustomization")
    
    local failed_files=()
    local validated_count=0
    
    while IFS= read -r file; do
        # Skip non-Kubernetes files
        if ! grep -q "apiVersion\|kind:" "$file" 2>/dev/null; then
            continue
        fi
        
        # Skip template files
        if echo "$file" | grep -q "template"; then
            continue
        fi
        
        log_info "Validating K8s manifest: $file"
        
        if kubectl --dry-run=client apply -f "$file" >/dev/null 2>&1; then
            ((validated_count++))
        else
            failed_files+=("$file")
            log_error "Kubernetes validation failed for: $file"
        fi
    done <<< "$k8s_files"
    
    if [ ${#failed_files[@]} -eq 0 ]; then
        log_success "All $validated_count Kubernetes manifests are valid"
        return 0
    else
        log_error "Kubernetes validation failed for ${#failed_files[@]} files"
        return 1
    fi
}

# Validate Kustomize configurations
validate_kustomize() {
    log_info "Validating Kustomize configurations..."
    
    local kustomization_files
    kustomization_files=$(find . -name "kustomization.yaml" -o -name "kustomization.yml" | grep -v ".git")
    
    local failed_dirs=()
    local validated_count=0
    
    while IFS= read -r file; do
        local dir
        dir=$(dirname "$file")
        
        log_info "Validating Kustomize in: $dir"
        
        if (cd "$dir" && kustomize build . >/dev/null 2>&1); then
            ((validated_count++))
        else
            failed_dirs+=("$dir")
            log_error "Kustomize validation failed for: $dir"
        fi
    done <<< "$kustomization_files"
    
    if [ ${#failed_dirs[@]} -eq 0 ]; then
        log_success "All $validated_count Kustomize configurations are valid"
        return 0
    else
        log_error "Kustomize validation failed for ${#failed_dirs[@]} directories"
        return 1
    fi
}

# Validate ArgoCD applications
validate_argocd() {
    log_info "Validating ArgoCD applications..."
    
    if ! command -v argocd >/dev/null 2>&1; then
        log_warning "ArgoCD CLI not found, skipping ArgoCD validation"
        return 0
    fi
    
    local argocd_files
    argocd_files=$(find . -path "*/argocd/*.yaml" -o -path "*/argocd/*.yml" | grep -v ".git")
    
    if [ -z "$argocd_files" ]; then
        log_info "No ArgoCD applications found"
        return 0
    fi
    
    local failed_files=()
    local validated_count=0
    
    while IFS= read -r file; do
        log_info "Validating ArgoCD app: $file"
        
        if argocd app validate "$file" >/dev/null 2>&1; then
            ((validated_count++))
        else
            failed_files+=("$file")
            log_error "ArgoCD validation failed for: $file"
        fi
    done <<< "$argocd_files"
    
    if [ ${#failed_files[@]} -eq 0 ]; then
        log_success "All $validated_count ArgoCD applications are valid"
        return 0
    else
        log_error "ArgoCD validation failed for ${#failed_files[@]} files"
        return 1
    fi
}

# Check for common issues
check_common_issues() {
    log_info "Checking for common issues..."
    
    local issues_found=0
    
    # Check for missing resource limits
    log_info "Checking for missing resource limits..."
    local deployments_without_limits
    deployments_without_limits=$(find . -name "*.yaml" -o -name "*.yml" | xargs grep -l "kind: Deployment\|kind: StatefulSet" | xargs grep -L "resources:" || true)
    
    if [ -n "$deployments_without_limits" ]; then
        log_warning "Deployments/StatefulSets without resource limits found:"
        echo "$deployments_without_limits"
        ((issues_found++))
    fi
    
    # Check for hardcoded image tags
    log_info "Checking for hardcoded image tags..."
    local hardcoded_tags
    hardcoded_tags=$(find . -name "*.yaml" -o -name "*.yml" | xargs grep -n "image:.*:latest\|image:.*:v[0-9]" | grep -v "newTag\|# " || true)
    
    if [ -n "$hardcoded_tags" ]; then
        log_warning "Hardcoded image tags found:"
        echo "$hardcoded_tags"
        ((issues_found++))
    fi
    
    # Check for missing labels
    log_info "Checking for missing standard labels..."
    local missing_labels
    missing_labels=$(find . -name "*.yaml" -o -name "*.yml" | xargs grep -l "kind: Deployment\|kind: Service\|kind: Ingress" | xargs grep -L "app:\|application:\|version:" || true)
    
    if [ -n "$missing_labels" ]; then
        log_warning "Resources without standard labels found:"
        echo "$missing_labels"
        ((issues_found++))
    fi
    
    if [ $issues_found -eq 0 ]; then
        log_success "No common issues found"
    else
        log_warning "Found $issues_found potential issues (warnings only)"
    fi
}

# Main validation function
main() {
    log_info "Starting SurveySparrow K8s manifests validation..."
    
    local exit_code=0
    
    check_dependencies
    
    if ! validate_yaml_syntax; then
        exit_code=1
    fi
    
    if ! validate_kubernetes_manifests; then
        exit_code=1
    fi
    
    if ! validate_kustomize; then
        exit_code=1
    fi
    
    if ! validate_argocd; then
        exit_code=1
    fi
    
    check_common_issues
    
    if [ $exit_code -eq 0 ]; then
        log_success "All validations passed! ✅"
    else
        log_error "Some validations failed! ❌"
    fi
    
    exit $exit_code
}

# Run main function
main "$@"
