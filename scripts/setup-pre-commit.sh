#!/bin/bash

# SurveySparrow K8s Manifests - Pre-commit Setup Script
# This script sets up the complete pre-commit workflow

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on supported OS
check_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
    else
        log_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
    log_info "Detected OS: $OS"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Install dependencies based on OS
install_dependencies() {
    log_info "Installing system dependencies..."
    
    case $OS in
        "macos")
            if ! command_exists brew; then
                log_error "Homebrew not found. Please install Homebrew first:"
                log_info "https://brew.sh/"
                exit 1
            fi
            
            # Install dependencies with Homebrew
            log_info "Installing dependencies with Homebrew..."
            brew install node kubectl kustomize yamllint pre-commit
            
            # Optional: Install ArgoCD CLI
            if ! command_exists argocd; then
                log_info "Installing ArgoCD CLI..."
                brew install argocd
            fi
            ;;
            
        "linux")
            # Install Node.js
            if ! command_exists node; then
                log_info "Installing Node.js..."
                curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
                sudo apt-get install -y nodejs
            fi
            
            # Install kubectl
            if ! command_exists kubectl; then
                log_info "Installing kubectl..."
                curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
                sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
                rm kubectl
            fi
            
            # Install kustomize
            if ! command_exists kustomize; then
                log_info "Installing kustomize..."
                curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh" | bash
                sudo mv kustomize /usr/local/bin/
            fi
            
            # Install yamllint
            if ! command_exists yamllint; then
                log_info "Installing yamllint..."
                sudo apt-get update && sudo apt-get install -y yamllint
            fi
            
            # Install pre-commit
            if ! command_exists pre-commit; then
                log_info "Installing pre-commit..."
                pip3 install pre-commit
            fi
            
            # Optional: Install ArgoCD CLI
            if ! command_exists argocd; then
                log_info "Installing ArgoCD CLI..."
                curl -sSL -o argocd-linux-amd64 https://github.com/argoproj/argo-cd/releases/latest/download/argocd-linux-amd64
                sudo install -m 555 argocd-linux-amd64 /usr/local/bin/argocd
                rm argocd-linux-amd64
            fi
            ;;
    esac
}

# Verify dependencies
verify_dependencies() {
    log_info "Verifying dependencies..."
    
    local missing_deps=()
    
    if ! command_exists node; then
        missing_deps+=("node")
    fi
    
    if ! command_exists npm; then
        missing_deps+=("npm")
    fi
    
    if ! command_exists kubectl; then
        missing_deps+=("kubectl")
    fi
    
    if ! command_exists kustomize; then
        missing_deps+=("kustomize")
    fi
    
    if ! command_exists yamllint; then
        missing_deps+=("yamllint")
    fi
    
    if ! command_exists pre-commit; then
        missing_deps+=("pre-commit")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing dependencies: ${missing_deps[*]}"
        return 1
    fi
    
    log_success "All dependencies are available"
    
    # Show versions
    log_info "Dependency versions:"
    echo "  Node.js: $(node --version)"
    echo "  npm: $(npm --version)"
    echo "  kubectl: $(kubectl version --client --short 2>/dev/null || echo 'Not connected to cluster')"
    echo "  kustomize: $(kustomize version --short 2>/dev/null || echo 'Unknown')"
    echo "  yamllint: $(yamllint --version)"
    echo "  pre-commit: $(pre-commit --version)"
    
    if command_exists argocd; then
        echo "  argocd: $(argocd version --client --short 2>/dev/null || echo 'Unknown')"
    else
        log_warning "ArgoCD CLI not installed (optional)"
    fi
}

# Install Node.js dependencies
install_node_dependencies() {
    log_info "Installing Node.js dependencies..."
    
    if [ ! -f "package.json" ]; then
        log_error "package.json not found. Are you in the repository root?"
        exit 1
    fi
    
    npm install
    log_success "Node.js dependencies installed"
}

# Setup pre-commit hooks
setup_pre_commit_hooks() {
    log_info "Setting up pre-commit hooks..."
    
    # Install pre-commit hooks
    pre-commit install --hook-type commit-msg --hook-type pre-commit
    
    # Install husky hooks
    npx husky install
    
    # Make hook scripts executable
    chmod +x .husky/pre-commit .husky/commit-msg
    
    log_success "Pre-commit hooks installed"
}

# Initialize secrets baseline
initialize_secrets_baseline() {
    log_info "Initializing secrets baseline..."
    
    if command_exists detect-secrets; then
        detect-secrets scan --baseline .secrets.baseline
        log_success "Secrets baseline initialized"
    else
        log_warning "detect-secrets not found, installing..."
        pip3 install detect-secrets
        detect-secrets scan --baseline .secrets.baseline
        log_success "Secrets baseline initialized"
    fi
}

# Run validation tests
run_validation_tests() {
    log_info "Running validation tests..."
    
    # Test YAML linting
    log_info "Testing YAML linting..."
    if yamllint --version >/dev/null 2>&1; then
        log_success "YAML linting test passed"
    else
        log_error "YAML linting test failed"
        return 1
    fi
    
    # Test Kubernetes validation script
    log_info "Testing Kubernetes validation script..."
    if [ -x "./scripts/validate-k8s.sh" ]; then
        log_success "Kubernetes validation script is executable"
    else
        log_error "Kubernetes validation script not found or not executable"
        return 1
    fi
    
    # Test commitlint
    log_info "Testing commitlint..."
    if echo "feat: test commit message" | npx commitlint >/dev/null 2>&1; then
        log_success "Commitlint test passed"
    else
        log_error "Commitlint test failed"
        return 1
    fi
    
    # Test pre-commit hooks (dry run)
    log_info "Testing pre-commit hooks..."
    if pre-commit run --all-files --show-diff-on-failure >/dev/null 2>&1; then
        log_success "Pre-commit hooks test passed"
    else
        log_warning "Pre-commit hooks test had warnings (this is normal for initial setup)"
    fi
    
    log_success "All validation tests completed"
}

# Display setup summary
display_summary() {
    log_success "🎉 Pre-commit workflow setup completed!"
    
    echo ""
    echo "📋 Setup Summary:"
    echo "  ✅ System dependencies installed"
    echo "  ✅ Node.js dependencies installed"
    echo "  ✅ Pre-commit hooks configured"
    echo "  ✅ Husky Git hooks installed"
    echo "  ✅ Secrets baseline initialized"
    echo "  ✅ Validation tests passed"
    
    echo ""
    echo "🚀 Next Steps:"
    echo "  1. Make a test commit: npm run commit"
    echo "  2. Read the contributing guide: cat CONTRIBUTING.md"
    echo "  3. Review pre-commit config: cat .pre-commit-config.yaml"
    echo "  4. Run validations: ./scripts/validate-k8s.sh"
    
    echo ""
    echo "📚 Useful Commands:"
    echo "  npm run commit          - Guided commit with commitizen"
    echo "  npm run pre-commit      - Run all pre-commit hooks"
    echo "  npm run changelog       - Generate changelog"
    echo "  ./scripts/validate-k8s.sh - Validate K8s manifests"
    
    echo ""
    echo "🔧 Configuration Files Created/Updated:"
    echo "  📄 package.json"
    echo "  📄 .czrc"
    echo "  📄 commitlint.config.js"
    echo "  📄 .pre-commit-config.yaml"
    echo "  📄 .husky/pre-commit"
    echo "  📄 .husky/commit-msg"
    echo "  📄 .secrets.baseline"
    echo "  📄 CONTRIBUTING.md"
    echo "  📄 docs/PRE_COMMIT_SETUP.md"
}

# Main setup function
main() {
    log_info "🚀 Starting SurveySparrow K8s Manifests pre-commit setup..."
    
    check_os
    install_dependencies
    verify_dependencies
    install_node_dependencies
    setup_pre_commit_hooks
    initialize_secrets_baseline
    run_validation_tests
    display_summary
    
    log_success "Setup completed successfully! 🎉"
}

# Handle script arguments
case "${1:-}" in
    "--help"|"-h")
        echo "SurveySparrow K8s Manifests Pre-commit Setup Script"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --verify-only  Only verify existing setup"
        echo ""
        echo "This script will install and configure:"
        echo "  - System dependencies (kubectl, kustomize, yamllint, etc.)"
        echo "  - Node.js dependencies (commitizen, commitlint, etc.)"
        echo "  - Pre-commit hooks and Git hooks"
        echo "  - Validation tools and scripts"
        exit 0
        ;;
    "--verify-only")
        log_info "Verifying existing setup..."
        verify_dependencies
        run_validation_tests
        log_success "Verification completed!"
        exit 0
        ;;
    "")
        main
        ;;
    *)
        log_error "Unknown option: $1"
        log_info "Use --help for usage information"
        exit 1
        ;;
esac
