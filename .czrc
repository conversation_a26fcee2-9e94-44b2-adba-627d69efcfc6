{"path": "cz-conventional-changelog", "maxHeaderWidth": 100, "maxLineWidth": 100, "defaultType": "", "defaultScope": "", "defaultSubject": "", "defaultBody": "", "defaultIssues": "", "disableScopeLowerCase": false, "disableSubjectLowerCase": false, "format": "{type}{scope}: {subject}", "types": {"feat": {"description": "✨ A new feature (e.g., new service deployment, new environment)", "title": "Features", "emoji": "✨"}, "fix": {"description": "🐛 A bug fix (e.g., configuration error, deployment issue)", "title": "Bug Fixes", "emoji": "🐛"}, "docs": {"description": "📚 Documentation only changes (README, comments, guides)", "title": "Documentation", "emoji": "📚"}, "style": {"description": "💎 Changes that do not affect the meaning (formatting, missing semi-colons)", "title": "Styles", "emoji": "💎"}, "refactor": {"description": "📦 A code change that neither fixes a bug nor adds a feature", "title": "Code Refactoring", "emoji": "📦"}, "perf": {"description": "🚀 A change that improves performance", "title": "Performance Improvements", "emoji": "🚀"}, "test": {"description": "🚨 Adding missing tests or correcting existing tests", "title": "Tests", "emoji": "🚨"}, "build": {"description": "🛠 Changes that affect the build system or external dependencies", "title": "Builds", "emoji": "🛠"}, "ci": {"description": "⚙️ Changes to CI configuration files and scripts (ArgoCD, pipelines)", "title": "Continuous Integrations", "emoji": "⚙️"}, "chore": {"description": "♻️ Other changes that don't modify manifests or test files", "title": "Chores", "emoji": "♻️"}, "revert": {"description": "🗑 Reverts a previous commit", "title": "Reverts", "emoji": "🗑"}, "security": {"description": "🔒 Security improvements or fixes", "title": "Security", "emoji": "🔒"}, "config": {"description": "⚙️ Configuration changes (environment variables, secrets)", "title": "Configuration", "emoji": "⚙️"}}, "scopes": ["esting", "argocd", "kustomize", "deployment", "service", "ingress", "configmap", "secret", "hpa", "pdb", "rbac", "monitoring", "logging", "security", "networking", "storage", "backup", "migration", "policy", "strapi", "sparrowdesk", "sparrowpay", "surveys<PERSON>row", "edith", "production", "staging", "sandbox", "us-virginia", "berlin"]}