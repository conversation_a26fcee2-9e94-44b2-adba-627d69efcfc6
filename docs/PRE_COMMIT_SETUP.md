# Pre-commit Workflow Setup Guide

This guide provides step-by-step instructions for setting up the comprehensive pre-commit workflow for the SurveySparrow K8s manifests repository.

## 📋 Overview

Our pre-commit workflow includes:

- **Commitizen** for standardized commit messages
- **Commitlint** for commit message validation
- **Conventional Changelog** for automated changelog generation
- **Pre-commit hooks** for code quality and validation
- **<PERSON><PERSON>** for Git hooks management
- **Kubernetes-specific validations**

## 🛠 Installation Instructions

### Step 1: Install System Dependencies

#### macOS (using Homebrew)

```bash
# Install Node.js
brew install node

# Install kubectl
brew install kubectl

# Install kustomize
brew install kustomize

# Install yamllint
brew install yamllint

# Install pre-commit
brew install pre-commit

# Install ArgoCD CLI (optional)
brew install argocd
```

#### Ubuntu/Debian

```bash
# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# Install kustomize
curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh" | bash
sudo mv kustomize /usr/local/bin/

# Install yamllint
sudo apt-get install yamllint

# Install pre-commit
pip3 install pre-commit

# Install ArgoCD CLI (optional)
curl -sSL -o argocd-linux-amd64 https://github.com/argoproj/argo-cd/releases/latest/download/argocd-linux-amd64
sudo install -m 555 argocd-linux-amd64 /usr/local/bin/argocd
```

### Step 2: Repository Setup

1. **Clone and navigate to repository**:
   ```bash
   <NAME_EMAIL>:surveysparrow/sparrow-k8s-manifests.git
   cd sparrow-k8s-manifests
   ```

2. **Install Node.js dependencies**:
   ```bash
   npm install
   ```

3. **Install pre-commit hooks**:
   ```bash
   npm run install-hooks
   ```

4. **Initialize secrets baseline**:
   ```bash
   detect-secrets scan --baseline .secrets.baseline
   ```

### Step 3: Verify Installation

```bash
# Test pre-commit hooks
npm run pre-commit

# Test commit message validation
echo "test commit message" | npx commitlint

# Test Kubernetes validation
./scripts/validate-k8s.sh

# Test commitizen
npm run commit -- --dry-run
```

## 🔧 Configuration Details

### Commitizen Configuration

The `.czrc` file configures commitizen with:

- Custom commit types for Kubernetes workflows
- Predefined scopes for different components
- Emoji support for better visualization
- Integration with conventional changelog

### Commitlint Rules

The `commitlint.config.js` includes:

- Conventional commit format validation
- Custom rules for ticket ID format
- Scope validation for K8s components
- Subject and header length limits

### Pre-commit Hooks

The `.pre-commit-config.yaml` configures:

- **YAML validation** with yamllint
- **Kubernetes manifest validation** with kubectl
- **Kustomize validation** with kustomize build
- **ArgoCD application validation** with argocd CLI
- **Security scanning** with detect-secrets
- **Dockerfile linting** with hadolint
- **Markdown linting** with markdownlint

## 📝 Usage Examples

### Making a Commit

#### Option 1: Using Commitizen (Recommended)

```bash
# Stage your changes
git add .

# Use commitizen for guided commit
npm run commit
```

This will prompt you through:
1. Select commit type
2. Enter scope (optional)
3. Write description
4. Add body (optional)
5. Add breaking changes (if any)
6. Add issue references

#### Option 2: Manual Commit

```bash
# Stage your changes
git add .

# Write commit message following convention
git commit -m "feat(strapi): add production deployment configuration

Implements INFRA-123:
- Production-ready Strapi deployment
- Auto-scaling configuration
- Security hardening

Closes INFRA-123"
```

### Generating Changelog

```bash
# Generate changelog for current version
npm run changelog

# Generate changelog from scratch
npm run changelog:first

# Bump version and generate changelog
npm run version:patch  # for patch version
npm run version:minor  # for minor version
npm run version:major  # for major version
```

### Running Validations

```bash
# Run all pre-commit hooks
npm run pre-commit

# Run specific validations
npm run lint:yaml
npm run validate:kustomize
npm run validate:argocd

# Run custom K8s validation script
./scripts/validate-k8s.sh
```

## 🚨 Troubleshooting

### Common Issues and Solutions

#### 1. Pre-commit hooks not running

```bash
# Reinstall hooks
pre-commit uninstall
npm run install-hooks

# Check hook installation
ls -la .git/hooks/
```

#### 2. Commitlint failing

```bash
# Test commit message format
echo "your commit message" | npx commitlint

# Use commitizen for proper format
npm run commit
```

#### 3. YAML validation errors

```bash
# Check specific file
yamllint path/to/file.yaml

# Fix common issues
# - Check indentation (2 spaces)
# - Remove trailing spaces
# - Fix line length (max 120 chars)
```

#### 4. Kubernetes validation failing

```bash
# Check kubectl connection
kubectl cluster-info

# Validate specific file
kubectl --dry-run=client apply -f path/to/manifest.yaml

# Check for common issues:
# - Missing required fields
# - Invalid resource types
# - Incorrect API versions
```

#### 5. Kustomize build errors

```bash
# Navigate to kustomization directory
cd path/to/kustomization

# Test build
kustomize build .

# Common fixes:
# - Check resource paths
# - Verify patch targets
# - Fix YAML syntax
```

### Debug Mode

Enable debug mode for detailed output:

```bash
# Enable pre-commit debug
HUSKY_DEBUG=1 git commit -m "test"

# Enable commitlint debug
DEBUG=commitlint* npx commitlint --edit

# Enable yamllint verbose output
yamllint -f parsable .
```

## 🔄 Integration with Existing Workflow

### Bitbucket Integration

The pre-commit workflow integrates with Bitbucket:

1. **Branch Protection**: Configure branch protection rules
2. **PR Requirements**: Require passing pre-commit checks
3. **Automated Checks**: Use Bitbucket Pipelines for CI validation

### ArgoCD Integration

For ArgoCD applications:

1. **Validation**: Pre-commit hooks validate ArgoCD manifests
2. **Sync Policies**: Ensure proper sync configuration
3. **Health Checks**: Validate application health checks

### Team Workflow

1. **Developer commits** → Pre-commit hooks run
2. **Push to feature branch** → CI validation
3. **Create PR** → Additional validation
4. **Merge to release** → Changelog generation
5. **Deploy to environments** → ArgoCD sync

## 📊 Metrics and Monitoring

Track pre-commit workflow effectiveness:

```bash
# Check hook execution times
pre-commit run --all-files --verbose

# Analyze commit message compliance
git log --oneline --grep="^(feat|fix|docs)" --since="1 month ago"

# Monitor validation failures
grep "pre-commit" .git/hooks/pre-commit.log
```

## 🎯 Best Practices

1. **Commit Frequently**: Make small, focused commits
2. **Use Commitizen**: Leverage guided commit process
3. **Test Locally**: Run validations before pushing
4. **Document Changes**: Include meaningful commit bodies
5. **Reference Tickets**: Link commits to issue tracking
6. **Review Hooks**: Regularly update pre-commit configuration

## 📚 Additional Resources

- [Commitizen Documentation](https://commitizen-tools.github.io/commitizen/)
- [Commitlint Documentation](https://commitlint.js.org/)
- [Pre-commit Documentation](https://pre-commit.com/)
- [Conventional Commits](https://www.conventionalcommits.org/)
- [Husky Documentation](https://typicode.github.io/husky/)

---

For questions or issues, contact the DevOps team or create an issue in the repository.
