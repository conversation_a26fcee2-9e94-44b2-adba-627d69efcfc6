apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-port: traffic-port
    alb.ingress.kubernetes.io/healthcheck-path: /status
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '30'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '10'
    alb.ingress.kubernetes.io/success-codes: '200'
    alb.ingress.kubernetes.io/healthy-threshold-count: '2'
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '2'
    alb.ingress.kubernetes.io/subnets: subnet-0269b378cc058d1ff, subnet-0af63ff92ec5f32ef, subnet-0db297fceca4c1655
    alb.ingress.kubernetes.io/security-groups: sg-0982672b8639c3ba5
    alb.ingress.kubernetes.io/load-balancer-name: sg-staging-public-alb
    alb.ingress.kubernetes.io/group.name: sg-staging-public-ingress-group
    alb.ingress.kubernetes.io/load-balancer-attributes: deletion_protection.enabled=true
    alb.ingress.kubernetes.io/actions.robots-response: '{"type": "fixed-response","fixedResponseConfig": {"contentType": "text/plain","statusCode": "200","messageBody": "User-agent: * \nDisallow: "}}'
    alb.ingress.kubernetes.io/tags: "CreatedBy=<EMAIL>,Team=geniestaging,Service=geniestaging"

spec:
  ingressClassName: my-aws-ingress-class
  rules: [] # Placeholder for rules in the overlays
