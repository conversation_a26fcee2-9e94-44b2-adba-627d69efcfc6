apiVersion: apps/v1
kind: Deployment
metadata:
  name: application
spec:
  template:
    spec:
      serviceAccountName: batman-application-sa
      volumes:
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: batman-secrets
      containers:
      - name: genie-app
        envFrom:
        - configMapRef:
            name: batman-genie-configmap
        - secretRef:
            name: batman-secrets
        env:
        - name: SPARROW_GENIE_BUNDLE_FINGERPRINT
          value: 3c01a8e46ffcff8b83e3d160f80213dc8f8b9072
