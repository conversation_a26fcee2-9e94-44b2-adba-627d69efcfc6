apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:225989329684:certificate/f3f08659-b267-4267-aed1-1ba10414db4c
spec:
  rules:
    - host: "www.geniestaging.com"
      http:
        paths:
        - path: /robots.txt
          pathType: Exact
          backend:
            service:
              name: robots-response
              port:
                name: use-annotation
        - path: /admin
          pathType: Prefix
          backend:
            service:
              name: cms-service-geniestaging
              port:
                number: 3000
        - path: /
          pathType: Prefix
          backend:
            service:
              name: website-service-geniestaging
              port:
                number: 3000
    - host: geniestaging.com
      http:
        paths:
          - path: /robots.txt
            pathType: Exact
            backend:
              service:
                name: robots-response
                port:
                  name: use-annotation