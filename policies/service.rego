package main

import rego.v1

# =============================================================================
# Service Policy Rules
# =============================================================================

# RULE 1: Service must have required labels
deny contains {
  "msg": sprintf("Service '%v' missing required label: %v", [input.metadata.name, label]),
  "details": {
    "rule": "required_labels",
    "field": "metadata.labels",
    "missing_label": label,
    "recommendation": sprintf("Add label '%v' to service metadata", [label])
  }
} if {
  input.kind == "Service"
  required_labels := {"app", "environment"}
  some label in required_labels
  not input.metadata.labels[label]
}


# RULE 3: Service type should be appropriate
deny contains {
  "msg": sprintf("Service '%v' uses NodePort type which exposes services on all nodes", [input.metadata.name]),
  "details": {
    "rule": "avoid_nodeport",
    "field": "spec.type",
    "current_value": input.spec.type,
    "recommendation": "Use ClusterIP or LoadBalancer instead of NodePort for better security"
  }
} if {
  input.kind == "Service"
  input.spec.type == "NodePort"
}

# RULE 4: LoadBalancer services should have appropriate annotations
warn contains {
  "msg": sprintf("LoadBalancer service '%v' should specify load balancer class", [input.metadata.name]),
  "details": {
    "rule": "loadbalancer_class",
    "field": "spec.loadBalancerClass",
    "recommendation": "Specify loadBalancerClass for better control over load balancer provisioning"
  }
} if {
  input.kind == "Service"
  input.spec.type == "LoadBalancer"
  not input.spec.loadBalancerClass
}

# RULE 5: Service ports should have names
warn contains {
  "msg": sprintf("Service '%v' has unnamed port %v", [input.metadata.name, port.port]),
  "details": {
    "rule": "named_ports",
    "field": "spec.ports",
    "port": port.port,
    "recommendation": "Name all service ports for better readability and service mesh compatibility"
  }
} if {
  input.kind == "Service"
  some port in input.spec.ports
  not port.name
}

# RULE 6: Service ports should use standard port numbers
warn contains {
  "msg": sprintf("Service '%v' uses non-standard port %v for protocol %v", [input.metadata.name, port.port, port.protocol]),
  "details": {
    "rule": "standard_ports",
    "field": "spec.ports",
    "port": port.port,
    "protocol": port.protocol,
    "recommendation": "Consider using standard ports (80 for HTTP, 443 for HTTPS, etc.)"
  }
} if {
  input.kind == "Service"
  some port in input.spec.ports
  port.protocol == "TCP"
  port.name
  is_non_standard_port(port)
}

# RULE 7: Service should have selector
deny contains {
  "msg": sprintf("Service '%v' must have selector to target pods", [input.metadata.name]),
  "details": {
    "rule": "service_selector",
    "field": "spec.selector",
    "recommendation": "Add selector to target appropriate pods"
  }
} if {
  input.kind == "Service"
  input.spec.type != "ExternalName"
  not input.spec.selector
}

# RULE 8: Service selector should match common labels
warn contains {
  "msg": sprintf("Service '%v' selector should include 'app' label", [input.metadata.name]),
  "details": {
    "rule": "selector_app_label",
    "field": "spec.selector",
    "current_selector": input.spec.selector,
    "recommendation": "Include 'app' label in selector for better pod targeting"
  }
} if {
  input.kind == "Service"
  input.spec.selector
  not input.spec.selector.app
}

# RULE 9: Headless services should be properly configured
warn contains {
  "msg": sprintf("Headless service '%v' should have publishNotReadyAddresses set", [input.metadata.name]),
  "details": {
    "rule": "headless_service_config",
    "field": "spec.publishNotReadyAddresses",
    "recommendation": "Consider setting publishNotReadyAddresses for headless services"
  }
} if {
  input.kind == "Service"
  input.spec.clusterIP == "None"
  not input.spec.publishNotReadyAddresses
}

# RULE 10: External services should use proper configuration
deny contains {
  "msg": sprintf("ExternalName service '%v' must specify externalName", [input.metadata.name]),
  "details": {
    "rule": "external_name_required",
    "field": "spec.externalName",
    "recommendation": "Specify externalName for ExternalName type services"
  }
} if {
  input.kind == "Service"
  input.spec.type == "ExternalName"
  not input.spec.externalName
}

# RULE 11: Service should not expose privileged ports
deny contains {
  "msg": sprintf("Service '%v' exposes privileged port %v", [input.metadata.name, port.port]),
  "details": {
    "rule": "no_privileged_ports",
    "field": "spec.ports",
    "port": port.port,
    "recommendation": "Avoid exposing privileged ports (< 1024) in services"
  }
} if {
  input.kind == "Service"
  some port in input.spec.ports
  port.port < 1024
  port.port != 80
  port.port != 443
}

# RULE 12: Service should have session affinity configured appropriately
warn contains {
  "msg": sprintf("Service '%v' with multiple ports should consider session affinity", [input.metadata.name]),
  "details": {
    "rule": "session_affinity",
    "field": "spec.sessionAffinity",
    "current_value": input.spec.sessionAffinity,
    "recommendation": "Consider setting sessionAffinity to ClientIP for stateful applications"
  }
} if {
  input.kind == "Service"
  count(input.spec.ports) > 1
  input.spec.sessionAffinity == "None"
  input.metadata.labels.stateful == "true"
}

# =============================================================================
# Helper Functions
# =============================================================================

# Check if port is non-standard for common protocols
is_non_standard_port(port) if {
  port.name
  http_ports := {"http", "web", "api"}
  some name in http_ports
  contains(lower(port.name), name)
  port.port != 80
  port.port != 8080
  port.port != 3000
}

is_non_standard_port(port) if {
  port.name
  https_ports := {"https", "tls", "ssl"}
  some name in https_ports
  contains(lower(port.name), name)
  port.port != 443
  port.port != 8443
}

is_non_standard_port(port) if {
  port.name
  contains(lower(port.name), "grpc")
  port.port != 9090
  port.port != 50051
}

# =============================================================================
# Summary Functions
# =============================================================================

# Count total violations
service_violation_count := count(deny)

# Count total warnings
service_warning_count := count(warn)

# Security score for services
service_security_rules := 4  # Security-focused rules
service_security_violations := count([violation | some violation in deny; violation.details.rule in {
  "avoid_nodeport", "no_privileged_ports", "service_selector", "external_name_required"
}])

service_security_score := ((service_security_rules - service_security_violations) / service_security_rules) * 100

# Best practices score
service_best_practices_rules := 8  # Best practice rules
service_best_practices_violations := count([violation |
  some violation in deny;
  violation.details.rule in {
    "required_labels", "service_description", "named_ports", "standard_ports",
    "selector_app_label", "headless_service_config", "session_affinity", "loadbalancer_class"
  }
]) + count([violation |
  some violation in warn;
  violation.details.rule in {
    "required_labels", "service_description", "named_ports", "standard_ports",
    "selector_app_label", "headless_service_config", "session_affinity", "loadbalancer_class"
  }
])

service_best_practices_score := ((service_best_practices_rules - service_best_practices_violations) / service_best_practices_rules) * 100