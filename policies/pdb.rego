package main

import rego.v1

# =============================================================================
# PodDisruptionBudget Policy Rules
# =============================================================================

# RULE 1: PDB must have required labels
deny contains {
  "msg": sprintf("PDB '%v' missing required label: %v", [input.metadata.name, label]),
  "details": {
    "rule": "required_labels",
    "field": "metadata.labels",
    "missing_label": label,
    "recommendation": sprintf("Add label '%v' to PDB metadata", [label])
  }
} if {
  input.kind == "PodDisruptionBudget"
  input.apiVersion == "policy/v1"
  required_labels := {"app","environment"}
  some label in required_labels
  not input.metadata.labels[label]
}

# RULE 2: PDB must have selector
deny contains {
  "msg": sprintf("PDB '%v' must specify selector to target pods", [input.metadata.name]),
  "details": {
    "rule": "selector_required",
    "field": "spec.selector",
    "recommendation": "Specify selector to define which pods this PDB applies to"
  }
} if {
  input.kind == "PodDisruptionBudget"
  input.apiVersion == "policy/v1"
  not input.spec.selector
}

# RULE 3: PDB must specify either minAvailable or maxUnavailable
deny contains {
  "msg": sprintf("PDB '%v' must specify either minAvailable or maxUnavailable", [input.metadata.name]),
  "details": {
    "rule": "disruption_budget_required",
    "field": "spec.minAvailable/maxUnavailable",
    "recommendation": "Specify either minAvailable or maxUnavailable to define disruption budget"
  }
} if {
  input.kind == "PodDisruptionBudget"
  input.apiVersion == "policy/v1"
  not input.spec.minAvailable
  not input.spec.maxUnavailable
}

# RULE 4: PDB should not specify both minAvailable and maxUnavailable
deny contains {
  "msg": sprintf("PDB '%v' should not specify both minAvailable and maxUnavailable", [input.metadata.name]),
  "details": {
    "rule": "single_disruption_budget",
    "field": "spec.minAvailable/maxUnavailable",
    "min_available": input.spec.minAvailable,
    "max_unavailable": input.spec.maxUnavailable,
    "recommendation": "Specify either minAvailable OR maxUnavailable, not both"
  }
} if {
  input.kind == "PodDisruptionBudget"
  input.apiVersion == "policy/v1"
  input.spec.minAvailable
  input.spec.maxUnavailable
}

# RULE 5: PDB should have reasonable minAvailable for production
warn contains {
  "msg": sprintf("PDB '%v' minAvailable (%v) may be too low for production high availability", [input.metadata.name, input.spec.minAvailable]),
  "details": {
    "rule": "production_min_available",
    "field": "spec.minAvailable",
    "current_value": input.spec.minAvailable,
    "recommendation": "Consider setting minAvailable to at least 2 or 50% for production workloads"
  }
} if {
  input.kind == "PodDisruptionBudget"
  input.apiVersion == "policy/v1"
  input.metadata.labels.environment in {"production", "prod"}
  input.spec.minAvailable
  is_low_min_available(input.spec.minAvailable)
}

# RULE 6: PDB should have reasonable maxUnavailable for production
warn contains {
  "msg": sprintf("PDB '%v' maxUnavailable (%v) may be too high for production availability", [input.metadata.name, input.spec.maxUnavailable]),
  "details": {
    "rule": "production_max_unavailable",
    "field": "spec.maxUnavailable",
    "current_value": input.spec.maxUnavailable,
    "recommendation": "Consider setting maxUnavailable to at most 1 or 25% for production workloads"
  }
} if {
  input.kind == "PodDisruptionBudget"
  input.apiVersion == "policy/v1"
  input.metadata.labels.environment in {"production", "prod"}
  input.spec.maxUnavailable
  is_high_max_unavailable(input.spec.maxUnavailable)
}

# RULE 7: PDB selector should include app label
warn contains {
  "msg": sprintf("PDB '%v' selector should include 'app' label for better pod targeting", [input.metadata.name]),
  "details": {
    "rule": "selector_app_label",
    "field": "spec.selector.matchLabels",
    "current_selector": input.spec.selector.matchLabels,
    "recommendation": "Include 'app' label in selector for consistent pod targeting"
  }
} if {
  input.kind == "PodDisruptionBudget"
  input.apiVersion == "policy/v1"
  input.spec.selector.matchLabels
  not input.spec.selector.matchLabels.app
}

# RULE 8: PDB should have meaningful name
warn contains {
  "msg": sprintf("PDB '%v' should have descriptive name indicating target application", [input.metadata.name]),
  "details": {
    "rule": "descriptive_name",
    "field": "metadata.name",
    "current_name": input.metadata.name,
    "recommendation": "Use descriptive names like 'app-name-pdb' to clearly identify the target application"
  }
} if {
  input.kind == "PodDisruptionBudget"
  input.apiVersion == "policy/v1"
  not contains(input.metadata.name, "pdb")
  not contains(input.metadata.name, "disruption")
}

# RULE 9: PDB should not be too restrictive for single replica deployments
warn contains {
  "msg": sprintf("PDB '%v' may be too restrictive for single replica deployments", [input.metadata.name]),
  "details": {
    "rule": "single_replica_warning",
    "field": "spec.minAvailable",
    "current_value": input.spec.minAvailable,
    "recommendation": "For single replica deployments, consider using maxUnavailable: 0 or minAvailable: 1"
  }
} if {
  input.kind == "PodDisruptionBudget"
  input.apiVersion == "policy/v1"
  input.spec.minAvailable
  is_restrictive_for_single_replica(input.spec.minAvailable)
}

# RULE 11: PDB should use percentage values for scalable applications
warn contains {
  "msg": sprintf("PDB '%v' should use percentage values for better scalability", [input.metadata.name]),
  "details": {
    "rule": "percentage_values_recommended",
    "field": "spec.minAvailable/maxUnavailable",
    "current_min": input.spec.minAvailable,
    "current_max": input.spec.maxUnavailable,
    "recommendation": "Use percentage values (e.g., '50%') instead of absolute numbers for scalable applications"
  }
} if {
  input.kind == "PodDisruptionBudget"
  input.apiVersion == "policy/v1"
  input.metadata.labels.scalable == "true"
  not uses_percentage_values(input.spec)
}

# RULE 12: PDB should have appropriate selector specificity
warn contains {
  "msg": sprintf("PDB '%v' selector may be too broad and affect unintended pods", [input.metadata.name]),
  "details": {
    "rule": "selector_specificity",
    "field": "spec.selector.matchLabels",
    "current_selector": input.spec.selector.matchLabels,
    "recommendation": "Use specific selectors with multiple labels to avoid affecting unintended pods"
  }
} if {
  input.kind == "PodDisruptionBudget"
  input.apiVersion == "policy/v1"
  input.spec.selector.matchLabels
  count(input.spec.selector.matchLabels) < 2
}

# =============================================================================
# Helper Functions
# =============================================================================

# Check if minAvailable value is too low
is_low_min_available(minAvailable) if {
  # If it's a number and less than 2
  is_number(minAvailable)
  minAvailable < 2
}

is_low_min_available(minAvailable) if {
  # If it's a percentage and less than 50%
  is_string(minAvailable)
  endswith(minAvailable, "%")
  percentage := to_number(trim_suffix(minAvailable, "%"))
  percentage < 50
}

# Check if maxUnavailable value is too high
is_high_max_unavailable(maxUnavailable) if {
  # If it's a number and greater than 1
  is_number(maxUnavailable)
  maxUnavailable > 1
}

is_high_max_unavailable(maxUnavailable) if {
  # If it's a percentage and greater than 25%
  is_string(maxUnavailable)
  endswith(maxUnavailable, "%")
  percentage := to_number(trim_suffix(maxUnavailable, "%"))
  percentage > 25
}

# Check if PDB is too restrictive for single replica
is_restrictive_for_single_replica(minAvailable) if {
  is_number(minAvailable)
  minAvailable > 1
}

is_restrictive_for_single_replica(minAvailable) if {
  is_string(minAvailable)
  endswith(minAvailable, "%")
  percentage := to_number(trim_suffix(minAvailable, "%"))
  percentage > 100
}

# Check if spec uses percentage values
uses_percentage_values(spec) if {
  spec.minAvailable
  is_string(spec.minAvailable)
  endswith(spec.minAvailable, "%")
}

uses_percentage_values(spec) if {
  spec.maxUnavailable
  is_string(spec.maxUnavailable)
  endswith(spec.maxUnavailable, "%")
}

# =============================================================================
# Summary Functions
# =============================================================================

# Count total violations
pdb_violation_count := count(deny)

# Count total warnings
pdb_warning_count := count(warn)

# Security and reliability score
pdb_reliability_rules := 4  # Critical reliability rules
pdb_reliability_violations := count([violation | some violation in deny; violation.details.rule in {
  "required_labels", "selector_required", "disruption_budget_required", "single_disruption_budget"
}])

pdb_reliability_score := ((pdb_reliability_rules - pdb_reliability_violations) / pdb_reliability_rules) * 100

# Best practices score
pdb_best_practices_rules := 8  # Best practice rules
pdb_best_practices_violations := count([violation | 
  some violation in deny; 
  violation.details.rule in {
    "production_min_available", "production_max_unavailable", "selector_app_label", "descriptive_name",
    "single_replica_warning", "unhealthy_pod_eviction_policy", "percentage_values_recommended", "selector_specificity"
  }
]) + count([violation | 
  some violation in warn; 
  violation.details.rule in {
    "production_min_available", "production_max_unavailable", "selector_app_label", "descriptive_name",
    "single_replica_warning", "unhealthy_pod_eviction_policy", "percentage_values_recommended", "selector_specificity"
  }
])

pdb_best_practices_score := ((pdb_best_practices_rules - pdb_best_practices_violations) / pdb_best_practices_rules) * 100
