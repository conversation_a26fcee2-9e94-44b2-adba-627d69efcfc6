package main

import rego.v1

# =============================================================================
# EC2NodeClass Policy Rules (Karpenter)
# =============================================================================

# RULE 1: EC2NodeClass must have required labels
deny contains {
  "msg": sprintf("EC2NodeClass '%v' missing required label: %v", [input.metadata.name, label]),
  "details": {
    "rule": "required_labels",
    "field": "metadata.labels",
    "missing_label": label,
    "recommendation": sprintf("Add label '%v' to EC2NodeClass metadata", [label])
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  required_labels := {"app"}
  some label in required_labels
  not input.metadata.labels[label]
}

# RULE 2: EC2NodeClass must specify AMI family
deny contains {
  "msg": sprintf("EC2NodeClass '%v' must specify amiFamily", [input.metadata.name]),
  "details": {
    "rule": "ami_family_required",
    "field": "spec.amiFamily",
    "recommendation": "Specify amiFamily (e.g., AL2, Bottlerocket, Ubuntu)"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  not input.spec.amiFamily
}

# RULE 3: EC2NodeClass must have subnet selector
deny contains {
  "msg": sprintf("EC2NodeClass '%v' must specify subnetSelectorTerms", [input.metadata.name]),
  "details": {
    "rule": "subnet_selector_required",
    "field": "spec.subnetSelectorTerms",
    "recommendation": "Specify subnet selector to define which subnets nodes can be launched in"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  not input.spec.subnetSelectorTerms
}

# RULE 4: EC2NodeClass must have security group selector
deny contains {
  "msg": sprintf("EC2NodeClass '%v' must specify securityGroupSelectorTerms", [input.metadata.name]),
  "details": {
    "rule": "security_group_selector_required",
    "field": "spec.securityGroupSelectorTerms",
    "recommendation": "Specify security group selector to define which security groups to attach to nodes"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  not input.spec.securityGroupSelectorTerms
}

# RULE 5: EC2NodeClass metadata options should require tokens
deny contains {
  "msg": sprintf("EC2NodeClass '%v' must require IMDSv2 tokens for security", [input.metadata.name]),
  "details": {
    "rule": "imdsv2_required",
    "field": "spec.metadataOptions.httpTokens",
    "current_value": input.spec.metadataOptions.httpTokens,
    "recommendation": "Set httpTokens to 'required' to enforce IMDSv2"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  input.spec.metadataOptions
  input.spec.metadataOptions.httpTokens != "required"
}

# RULE 6: EC2NodeClass block devices should be encrypted
deny contains {
  "msg": sprintf("EC2NodeClass '%v' block device '%v' must be encrypted", [input.metadata.name, device.deviceName]),
  "details": {
    "rule": "block_device_encryption",
    "field": "spec.blockDeviceMappings",
    "device_name": device.deviceName,
    "recommendation": "Set encrypted: true for all block devices"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  some device in input.spec.blockDeviceMappings
  device.ebs
  not device.ebs.encrypted == true
}

# RULE 6a: EC2NodeClass block devices must have deleteOnTermination enabled
deny contains {
  "msg": sprintf("EC2NodeClass '%v' block device '%v' must have deleteOnTermination enabled to prevent orphaned volumes", [input.metadata.name, device.deviceName]),
  "details": {
    "rule": "delete_on_termination_required",
    "field": "spec.blockDeviceMappings",
    "device_name": device.deviceName,
    "current_value": device.ebs.deleteOnTermination,
    "recommendation": "Set deleteOnTermination: true to automatically delete EBS volumes when instances terminate"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  some device in input.spec.blockDeviceMappings
  device.ebs
  not device.ebs.deleteOnTermination == true
}

# RULE 7: EC2NodeClass should not expose secrets in user data
deny contains {
  "msg": sprintf("EC2NodeClass '%v' user data should not contain hardcoded secrets", [input.metadata.name]),
  "details": {
    "rule": "no_secrets_in_userdata",
    "field": "spec.userData",
    "recommendation": "Use AWS Systems Manager Parameter Store or Secrets Manager instead of hardcoded secrets"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  input.spec.userData
  contains_potential_secret(input.spec.userData)
}

# RULE 8: EC2NodeClass should use appropriate AMI family for production
warn contains {
  "msg": sprintf("EC2NodeClass '%v' should use AL2 or Bottlerocket for production workloads", [input.metadata.name]),
  "details": {
    "rule": "production_ami_family",
    "field": "spec.amiFamily",
    "current_value": input.spec.amiFamily,
    "recommendation": "Use AL2 or Bottlerocket AMI families for production environments"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  input.metadata.labels.environment in {"production", "prod"}
  not input.spec.amiFamily in {"AL2023", "Bottlerocket"}
}

# RULE 9: EC2NodeClass should have proper instance store policy
warn contains {
  "msg": sprintf("EC2NodeClass '%v' should configure instance store policy for better performance", [input.metadata.name]),
  "details": {
    "rule": "instance_store_policy",
    "field": "spec.instanceStorePolicy",
    "recommendation": "Configure instanceStorePolicy (RAID0 or NVME) for instances with local storage"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  not input.spec.instanceStorePolicy
}

# RULE 10: EC2NodeClass should have metadata options configured securely
warn contains {
  "msg": sprintf("EC2NodeClass '%v' should configure metadata options for security", [input.metadata.name]),
  "details": {
    "rule": "metadata_options_security",
    "field": "spec.metadataOptions",
    "recommendation": "Configure metadata options with httpTokens: required and httpPutResponseHopLimit: 2"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  not input.spec.metadataOptions
}

# RULE 11: EC2NodeClass should limit metadata hop count
warn contains {
  "msg": sprintf("EC2NodeClass '%v' should limit metadata hop count for security", [input.metadata.name]),
  "details": {
    "rule": "metadata_hop_limit",
    "field": "spec.metadataOptions.httpPutResponseHopLimit",
    "current_value": input.spec.metadataOptions.httpPutResponseHopLimit,
    "recommendation": "Set httpPutResponseHopLimit to 2 or lower to limit metadata access"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  input.spec.metadataOptions
  input.spec.metadataOptions.httpPutResponseHopLimit > 2
}

# RULE 12: EC2NodeClass should have proper block device mappings
warn contains {
  "msg": sprintf("EC2NodeClass '%v' should configure block device mappings for root volume", [input.metadata.name]),
  "details": {
    "rule": "block_device_mappings",
    "field": "spec.blockDeviceMappings",
    "recommendation": "Configure block device mappings to specify root volume size and encryption"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  not input.spec.blockDeviceMappings
}

# RULE 13: EC2NodeClass should have appropriate root volume size
warn contains {
  "msg": sprintf("EC2NodeClass '%v' root volume size (%vGi) may be insufficient for production", [input.metadata.name, device.ebs.volumeSize]),
  "details": {
    "rule": "root_volume_size",
    "field": "spec.blockDeviceMappings",
    "device_name": device.deviceName,
    "current_size": device.ebs.volumeSize,
    "recommendation": "Consider at least 50Gi for production workloads"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  input.metadata.labels.environment in {"production", "prod"}
  some device in input.spec.blockDeviceMappings
  device.deviceName in {"/dev/xvda", "/dev/sda1"}
  device.ebs.volumeSize < 50
}

# RULE 14: EC2NodeClass should have proper tagging
warn contains {
  "msg": sprintf("EC2NodeClass '%v' should have comprehensive tags for cost tracking", [input.metadata.name]),
  "details": {
    "rule": "comprehensive_tagging",
    "field": "spec.tags",
    "missing_tags": missing_tags,
    "recommendation": "Add tags for Environment, Team, Project, and CostCenter"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  required_tags := {"Environment", "Team", "Project", "CostCenter"}
  missing_tags := [tag | some tag in required_tags; not input.spec.tags[tag]]
  count(missing_tags) > 0
}

# RULE 15: EC2NodeClass should use appropriate instance profile
warn contains {
  "msg": sprintf("EC2NodeClass '%v' should specify instance profile for proper IAM permissions", [input.metadata.name]),
  "details": {
    "rule": "instance_profile_recommended",
    "field": "spec.role",
    "recommendation": "Specify IAM role/instance profile with minimal required permissions"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  not input.spec.role
}

# RULE 17: EC2NodeClass should use gp3 volumes for better performance
warn contains {
  "msg": sprintf("EC2NodeClass '%v' should use gp3 volume type for better performance and cost", [input.metadata.name]),
  "details": {
    "rule": "gp3_volume_type",
    "field": "spec.blockDeviceMappings",
    "device_name": device.deviceName,
    "current_type": device.ebs.volumeType,
    "recommendation": "Use gp3 volume type for better performance and cost optimization"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  some device in input.spec.blockDeviceMappings
  device.ebs
  device.ebs.volumeType
  device.ebs.volumeType != "gp3"
}

# RULE 18: EC2NodeClass should have appropriate subnet diversity
warn contains {
  "msg": sprintf("EC2NodeClass '%v' should select subnets across multiple AZs for high availability", [input.metadata.name]),
  "details": {
    "rule": "subnet_diversity",
    "field": "spec.subnetSelectorTerms",
    "recommendation": "Ensure subnet selector includes subnets from multiple availability zones"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  count(input.spec.subnetSelectorTerms) == 1
}

# RULE 19: EC2NodeClass should not use deprecated AMI selectors
warn contains {
  "msg": sprintf("EC2NodeClass '%v' should use amiSelectorTerms instead of deprecated amiSelector", [input.metadata.name]),
  "details": {
    "rule": "ami_selector_terms",
    "field": "spec.amiSelectorTerms",
    "recommendation": "Use amiSelectorTerms for more flexible AMI selection"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  input.spec.amiSelector
  not input.spec.amiSelectorTerms
}

# RULE 20: EC2NodeClass should have termination grace period
warn contains {
  "msg": sprintf("EC2NodeClass '%v' should configure termination grace period for graceful shutdowns", [input.metadata.name]),
  "details": {
    "rule": "termination_grace_period",
    "field": "spec.terminationGracePeriod",
    "recommendation": "Set terminationGracePeriod to allow graceful pod termination (e.g., 30s)"
  }
} if {
  input.kind == "EC2NodeClass"
  input.apiVersion in {"karpenter.k8s.aws/v1beta1", "karpenter.k8s.aws/v1"}
  not input.spec.terminationGracePeriod
}

# =============================================================================
# Helper Functions
# =============================================================================

# Check for potential secrets in user data
contains_potential_secret(userData) if {
  secret_patterns := ["password", "secret", "key", "token", "credential"]
  some pattern in secret_patterns
  contains(lower(userData), pattern)
}

# Check if AMI family is production-ready
is_production_ami_family(amiFamily) if {
  amiFamily in {"AL2023", "Bottlerocket", "Ubuntu"}
}

# Check if instance type is appropriate for production
is_production_instance_type(instanceType) if {
  # Production should avoid nano, micro, small instances
  not contains(instanceType, "nano")
  not contains(instanceType, "micro")
  not contains(instanceType, "small")
}

# Check if volume type is modern and efficient
is_modern_volume_type(volumeType) if {
  volumeType in {"gp3"}
}

# Validate security group configuration
has_secure_security_groups(securityGroupTerms) if {
  count(securityGroupTerms) > 0
  # At least one security group selector is defined
}

# Check if subnet configuration provides high availability
provides_high_availability(subnetTerms) if {
  count(subnetTerms) > 1
}

# =============================================================================
# Summary Functions
# =============================================================================

# Count total violations
ec2nodeclass_violation_count := count(deny)

# Count total warnings
ec2nodeclass_warning_count := count(warn)

# Security score for EC2NodeClass
ec2nodeclass_security_rules := 8  # Security-focused rules
ec2nodeclass_security_violations := count([violation | some violation in deny; violation.details.rule in {
  "required_labels", "subnet_selector_required", "security_group_selector_required",
  "imdsv2_required", "block_device_encryption", "delete_on_termination_required",
  "no_secrets_in_userdata", "ami_family_required"
}])

ec2nodeclass_security_score := ((ec2nodeclass_security_rules - ec2nodeclass_security_violations) / ec2nodeclass_security_rules) * 100

# Best practices score
ec2nodeclass_best_practices_rules := 12  # Best practice rules
ec2nodeclass_best_practices_violations := count([violation |
  some violation in deny;
  violation.details.rule in {
    "production_ami_family", "instance_store_policy", "metadata_options_security",
    "metadata_hop_limit", "block_device_mappings", "root_volume_size", "comprehensive_tagging",
    "instance_profile_recommended", "gp3_volume_type", "subnet_diversity",
    "ami_selector_terms", "termination_grace_period"
  }
]) + count([violation |
  some violation in warn;
  violation.details.rule in {
    "production_ami_family", "instance_store_policy", "metadata_options_security",
    "metadata_hop_limit", "block_device_mappings", "root_volume_size", "comprehensive_tagging",
    "instance_profile_recommended", "gp3_volume_type", "subnet_diversity",
    "ami_selector_terms", "termination_grace_period"
  }
])

ec2nodeclass_best_practices_score := ((ec2nodeclass_best_practices_rules - ec2nodeclass_best_practices_violations) / ec2nodeclass_best_practices_rules) * 100

# Production readiness score
ec2nodeclass_production_rules := 7  # Production-specific rules
ec2nodeclass_production_violations := count([violation |
  some violation in deny;
  violation.details.rule in {
    "production_ami_family", "imdsv2_required", "block_device_encryption", "root_volume_size",
    "subnet_diversity", "comprehensive_tagging", "instance_profile_recommended"
  }
]) + count([violation |
  some violation in warn;
  violation.details.rule in {
    "production_ami_family", "imdsv2_required", "block_device_encryption", "root_volume_size",
    "subnet_diversity", "comprehensive_tagging", "instance_profile_recommended"
  }
])

ec2nodeclass_production_score := ((ec2nodeclass_production_rules - ec2nodeclass_production_violations) / ec2nodeclass_production_rules) * 100