package main

import rego.v1

# =============================================================================
# ArgoCD Application Best Practices and Security Rules
# =============================================================================

# RULE 1: Target revision must be 'master' or 'main' for production workloads
deny contains {
  "msg": sprintf("Target revision must be 'master' or 'main', found: %v", [input.spec.source.targetRevision]),
  "details": {
    "rule": "target_revision_validation",
    "field": "spec.source.targetRevision",
    "current_value": input.spec.source.targetRevision,
    "allowed_values": ["master", "main"]
  }
} if {
  input.kind == "Application"
  input.apiVersion == "argoproj.io/v1alpha1"
  not input.spec.source.targetRevision in {"master"}
}

# RULE 2: Repository URL must use SSH or HTTPS (no HTTP)
deny contains {
  "msg": sprintf("Repository URL must use SSH (git@) or HTTPS, found: %v", [input.spec.source.repoURL]),
  "details": {
    "rule": "secure_repository_url",
    "field": "spec.source.repoURL",
    "current_value": input.spec.source.repoURL
  }
} if {
  input.kind == "Application"
  input.apiVersion == "argoproj.io/v1alpha1"
  not startswith(input.spec.source.repoURL, "git@")
  not startswith(input.spec.source.repoURL, "https://")
}

# RULE 3: Application must have project specified
deny contains {
  "msg": "Application must have project specified",
  "details": {
    "rule": "project_required",
    "field": "spec.project",
    "recommendation": "Specify a project to enforce RBAC and security boundaries"
  }
} if {
  input.kind == "Application"
  input.apiVersion == "argoproj.io/v1alpha1"
  not input.spec.project
}

# RULE 5: Destination namespace should not be default or system namespaces
forbidden_namespaces := {"default", "kube-system", "kube-public", "kube-node-lease"}

deny contains {
  "msg": sprintf("Cannot deploy to system namespace: %v", [input.spec.destination.namespace]),
  "details": {
    "rule": "forbidden_destination_namespace",
    "field": "spec.destination.namespace",
    "current_value": input.spec.destination.namespace,
    "forbidden_namespaces": forbidden_namespaces
  }
} if {
  input.kind == "Application"
  input.apiVersion == "argoproj.io/v1alpha1"
  input.spec.destination.namespace in forbidden_namespaces
}

# RULE 6: Application should have sync policy configured for security
warn contains {
  "msg": "Consider configuring sync policy for better control over deployments",
  "details": {
    "rule": "sync_policy_recommendation",
    "field": "spec.syncPolicy",
    "recommendation": "Configure automated sync policies, prune policies, and self-heal settings"
  }
} if {
  input.kind == "Application"
  input.apiVersion == "argoproj.io/v1alpha1"
  not input.spec.syncPolicy
}

# RULE 7: Auto-sync should be disabled for production environments
deny contains {
  "msg": "Auto-sync should be disabled for production environments",
  "details": {
    "rule": "production_auto_sync",
    "field": "spec.syncPolicy.automated",
    "environment": input.metadata.labels.environment,
    "recommendation": "Disable automated sync for production workloads"
  }
} if {
  input.kind == "Application"
  input.apiVersion == "argoproj.io/v1alpha1"
  input.metadata.labels.environment in {"production", "prod"}
  input.spec.syncPolicy.automated
}

# RULE 8: Repository must be from trusted sources
trusted_repo_patterns := [
  "*****************:surveysparrow/",
  "https://github.com/surveysparrow/",
  "**************:surveysparrow/"
]

deny contains {
  "msg": sprintf("Repository must be from trusted sources, found: %v", [input.spec.source.repoURL]),
  "details": {
    "rule": "trusted_repository_source",
    "field": "spec.source.repoURL",
    "current_value": input.spec.source.repoURL,
    "trusted_patterns": trusted_repo_patterns
  }
} if {
  input.kind == "Application"
  input.apiVersion == "argoproj.io/v1alpha1"
  not repo_is_trusted(input.spec.source.repoURL)
}

# Helper function to check if repository is trusted
repo_is_trusted(repo_url) if {
  some pattern in trusted_repo_patterns
  startswith(repo_url, pattern)
}

# RULE 9: Application should have resource pruning enabled
warn contains {
  "msg": "Application should have resource pruning enabled",
  "details": {
    "rule": "prune_resources",
    "field": "spec.syncPolicy.automated.prune",
    "recommendation": "Enable pruning to remove resources that are no longer defined in Git"
  }
} if {
  input.kind == "Application"
  input.apiVersion == "argoproj.io/v1alpha1"
  input.spec.syncPolicy.automated
  not input.spec.syncPolicy.automated.prune
}

# RULE 10: Path should not point to root directory
deny contains {
  "msg": "Source path should not point to root directory for security",
  "details": {
    "rule": "secure_source_path",
    "field": "spec.source.path",
    "current_value": input.spec.source.path,
    "recommendation": "Use specific subdirectories instead of root"
  }
} if {
  input.kind == "Application"
  input.apiVersion == "argoproj.io/v1alpha1"
  input.spec.source.path in {"", ".", "/"}
}

# RULE 11: Application should have self-healing enabled
warn contains {
  "msg": "Application should have self-healing enabled",
  "details": {
    "rule": "self_healing",
    "field": "spec.syncPolicy.automated.selfHeal",
    "recommendation": "Enable self-healing to automatically sync when drift is detected"
  }
} if {
  input.kind == "Application"
  input.apiVersion == "argoproj.io/v1alpha1"
  input.spec.syncPolicy.automated
  not input.spec.syncPolicy.automated.selfHeal
}

# RULE 12: Validate destination server is secure
deny contains {
  "msg": sprintf("Destination server must use HTTPS, found: %v", [input.spec.destination.server]),
  "details": {
    "rule": "secure_destination_server",
    "field": "spec.destination.server",
    "current_value": input.spec.destination.server
  }
} if {
  input.kind == "Application"
  input.apiVersion == "argoproj.io/v1alpha1"
  input.spec.destination.server != "https://kubernetes.default.svc"
  not startswith(input.spec.destination.server, "https://")
}

# RULE 13: Application should have retry options for sync failures
warn contains {
  "msg": "Application should have retry options configured for sync failures",
  "details": {
    "rule": "sync_retry_options",
    "field": "spec.syncPolicy.retry",
    "recommendation": "Configure retry options to handle transient errors during sync"
  }
} if {
  input.kind == "Application"
  input.apiVersion == "argoproj.io/v1alpha1"
  input.spec.syncPolicy
  not input.spec.syncPolicy.retry
}

# RULE 14: Prevent applications from deploying to ArgoCD namespace
deny contains {
  "msg": "Applications cannot deploy to ArgoCD management namespace",
  "details": {
    "rule": "protect_argocd_namespace",
    "field": "spec.destination.namespace",
    "current_value": input.spec.destination.namespace,
    "forbidden_namespace": "argocd"
  }
} if {
  input.kind == "Application"
  input.apiVersion == "argoproj.io/v1alpha1"
  input.spec.destination.namespace == "argocd"
}

# RULE 15: Application should have sync options configured
warn contains {
  "msg": "Application should have sync options configured",
  "details": {
    "rule": "sync_options_recommended",
    "field": "spec.syncPolicy.syncOptions",
    "recommendation": "Configure sync options like CreateNamespace, PruneLast, etc."
  }
} if {
  input.kind == "Application"
  input.apiVersion == "argoproj.io/v1alpha1"
  input.spec.syncPolicy
  not input.spec.syncPolicy.syncOptions
}

# RULE 16: Application should have a revision history limit
warn contains {
  "msg": "Application should have a revision history limit",
  "details": {
    "rule": "revision_history_limit",
    "field": "spec.revisionHistoryLimit",
    "recommendation": "Set revisionHistoryLimit to control the number of stored revisions"
  }
} if {
  input.kind == "Application"
  input.apiVersion == "argoproj.io/v1alpha1"
  not input.spec.revisionHistoryLimit
}

# RULE 17: Application should have ignore differences configured for dynamic fields
warn contains {
  "msg": "Consider configuring ignoreDifferences for dynamic fields",
  "details": {
    "rule": "ignore_differences_recommended",
    "field": "spec.ignoreDifferences",
    "recommendation": "Configure ignoreDifferences for fields that change frequently or are managed externally"
  }
} if {
  input.kind == "Application"
  input.apiVersion == "argoproj.io/v1alpha1"
  not input.spec.ignoreDifferences
}

# =============================================================================
# Summary Functions
# =============================================================================

# Count total violations
violation_count := count(deny)

# Count total warnings  
warning_count := count(warn)

# Overall compliance status
compliance_status := "compliant" if {
  violation_count == 0
} else := "non-compliant"

# Security score (percentage of security rules passed)
security_rules := 8  # Security-focused rules
security_violations := count([violation | some violation in deny; violation.details.rule in {
  "secure_repository_url", "trusted_repository_source", "secure_destination_server",
  "protect_argocd_namespace", "forbidden_destination_namespace", "secure_source_path",
  "project_required", "production_auto_sync"
}])

security_score := ((security_rules - security_violations) / security_rules) * 100
