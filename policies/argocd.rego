package main

import rego.v1

# =============================================================================
# ArgoCD Application Best Practices and Security Rules
# =============================================================================

# RULE 1: Target revision must be 'master' or 'main' for production workloads
deny contains {
    "msg": sprintf("Target revision must be 'master' or 'main', found: %v", [input.spec.source.targetRevision]),
    "details": {
        "rule": "target_revision_validation",
        "field": "spec.source.targetRevision",
        "current_value": input.spec.source.targetRevision,
        "allowed_values": ["master", "main"]
    }
} if {
    input.kind == "Application"
    input.apiVersion == "argoproj.io/v1alpha1"
    not input.spec.source.targetRevision in {"master", "main"}
}

# RULE 2: Repository URL must use SSH or HTTPS (no HTTP)
deny contains {
    "msg": sprintf("Repository URL must use SSH (git@) or HTTPS, found: %v", [input.spec.source.repoURL]),
    "details": {
        "rule": "secure_repository_url",
        "field": "spec.source.repoURL",
        "current_value": input.spec.source.repoURL
    }
} if {
    input.kind == "Application"
    input.apiVersion == "argoproj.io/v1alpha1"
    not startswith(input.spec.source.repoURL, "git@")
    not startswith(input.spec.source.repoURL, "https://")
}

# RULE 3: Applications should not use the default project for production
warn contains {
    "msg": "Consider using a specific project instead of 'default' for better resource isolation",
    "details": {
        "rule": "avoid_default_project",
        "field": "spec.project",
        "current_value": input.spec.project,
        "recommendation": "Create dedicated projects for different environments/teams"
    }
} if {
    input.kind == "Application"
    input.apiVersion == "argoproj.io/v1alpha1"
    input.spec.project == "default"
}

# RULE 4: Application must have required labels
required_labels := {"app.kubernetes.io/name", "app.kubernetes.io/component", "environment"}

warn contains {
    "msg": sprintf("Application missing required label: %v", [missing_label]),
    "details": {
        "rule": "required_labels",
        "field": "metadata.labels",
        "missing_label": missing_label,
        "required_labels": required_labels
    }
} if {
    input.kind == "Application"
    input.apiVersion == "argoproj.io/v1alpha1"
    some missing_label in required_labels
    not input.metadata.labels[missing_label]
}

# RULE 5: Destination namespace should not be default or system namespaces
forbidden_namespaces := {"default", "kube-system", "kube-public", "kube-node-lease"}

deny contains {
    "msg": sprintf("Cannot deploy to system namespace: %v", [input.spec.destination.namespace]),
    "details": {
        "rule": "forbidden_destination_namespace",
        "field": "spec.destination.namespace",
        "current_value": input.spec.destination.namespace,
        "forbidden_namespaces": forbidden_namespaces
    }
} if {
    input.kind == "Application"
    input.apiVersion == "argoproj.io/v1alpha1"
    input.spec.destination.namespace in forbidden_namespaces
}

# RULE 6: Application should have sync policy configured for security
warn contains {
    "msg": "Consider configuring sync policy for better control over deployments",
    "details": {
        "rule": "sync_policy_recommendation",
        "field": "spec.syncPolicy",
        "recommendation": "Configure automated sync policies, prune policies, and self-heal settings"
    }
} if {
    input.kind == "Application"
    input.apiVersion == "argoproj.io/v1alpha1"
    not input.spec.syncPolicy
}

# RULE 7: Auto-sync should be disabled for production environments
deny contains {
    "msg": "Auto-sync should be disabled for production environments",
    "details": {
        "rule": "production_auto_sync",
        "field": "spec.syncPolicy.automated",
        "environment": input.metadata.labels.environment,
        "recommendation": "Disable automated sync for production workloads"
    }
} if {
    input.kind == "Application"
    input.apiVersion == "argoproj.io/v1alpha1"
    input.metadata.labels.environment in {"production", "prod"}
    input.spec.syncPolicy.automated
}

# RULE 8: Repository must be from trusted sources
trusted_repo_patterns := [
    "*****************:surveysparrow/",
    "https://github.com/surveysparrow/",
    "**************:surveysparrow/"
]

deny contains {
    "msg": sprintf("Repository must be from trusted sources, found: %v", [input.spec.source.repoURL]),
    "details": {
        "rule": "trusted_repository_source",
        "field": "spec.source.repoURL",
        "current_value": input.spec.source.repoURL,
        "trusted_patterns": trusted_repo_patterns
    }
} if {
    input.kind == "Application"
    input.apiVersion == "argoproj.io/v1alpha1"
    not repo_is_trusted(input.spec.source.repoURL)
}

# Helper function to check if repository is trusted
repo_is_trusted(repo_url) if {
    some pattern in trusted_repo_patterns
    startswith(repo_url, pattern)
}

# RULE 9: Application name should follow naming convention
deny contains {
    "msg": sprintf("Application name must follow naming convention: <service>-<environment>-<region>, found: %v", [input.metadata.name]),
    "details": {
        "rule": "naming_convention",
        "field": "metadata.name",
        "current_value": input.metadata.name,
        "pattern": "<service>-<environment>-<region>"
    }
} if {
    input.kind == "Application"
    input.apiVersion == "argoproj.io/v1alpha1"
    not regex.match(`^[a-z0-9-]+-[a-z0-9-]+-[a-z0-9-]+$`, input.metadata.name)
}

# RULE 10: Path should not point to root directory
deny contains {
    "msg": "Source path should not point to root directory for security",
    "details": {
        "rule": "secure_source_path",
        "field": "spec.source.path",
        "current_value": input.spec.source.path,
        "recommendation": "Use specific subdirectories instead of root"
    }
} if {
    input.kind == "Application"
    input.apiVersion == "argoproj.io/v1alpha1"
    input.spec.source.path in {"", ".", "/"}
}

# RULE 11: Application should have resource tracking labels
warn contains {
    "msg": "Consider adding resource tracking labels for better observability",
    "details": {
        "rule": "resource_tracking_labels",
        "field": "metadata.labels",
        "missing_labels": missing_tracking_labels,
        "recommendation": "Add labels like team, cost-center, or business-unit"
    }
} if {
    input.kind == "Application"
    input.apiVersion == "argoproj.io/v1alpha1"
    tracking_labels := {"team", "cost-center", "business-unit"}
    missing_tracking_labels := {label | some label in tracking_labels; not input.metadata.labels[label]}
    count(missing_tracking_labels) > 0
}

# RULE 12: Validate destination server is secure
deny contains {
    "msg": sprintf("Destination server must use HTTPS, found: %v", [input.spec.destination.server]),
    "details": {
        "rule": "secure_destination_server",
        "field": "spec.destination.server",
        "current_value": input.spec.destination.server
    }
} if {
    input.kind == "Application"
    input.apiVersion == "argoproj.io/v1alpha1"
    input.spec.destination.server != "https://kubernetes.default.svc"
    not startswith(input.spec.destination.server, "https://")
}

# RULE 13: Applications should have finalizers for proper cleanup
warn contains {
    "msg": "Consider adding finalizers for proper resource cleanup",
    "details": {
        "rule": "finalizers_recommendation",
        "field": "metadata.finalizers",
        "recommendation": "Add 'resources-finalizer.argocd.argoproj.io' for proper cleanup"
    }
} if {
    input.kind == "Application"
    input.apiVersion == "argoproj.io/v1alpha1"
    not input.metadata.finalizers
}

# RULE 14: Prevent applications from deploying to ArgoCD namespace
deny contains {
    "msg": "Applications cannot deploy to ArgoCD management namespace",
    "details": {
        "rule": "protect_argocd_namespace",
        "field": "spec.destination.namespace",
        "current_value": input.spec.destination.namespace,
        "forbidden_namespace": "argocd"
    }
} if {
    input.kind == "Application"
    input.apiVersion == "argoproj.io/v1alpha1"
    input.spec.destination.namespace == "argocd"
}

# =============================================================================
# Summary Functions
# =============================================================================

# Count total violations
violation_count := count(deny)

# Count total warnings  
warning_count := count(warn)

# Overall compliance status
compliance_status := "compliant" if {
    violation_count == 0
} else := "non-compliant"
