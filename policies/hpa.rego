package main

import rego.v1

# =============================================================================
# HorizontalPodAutoscaler Policy Rules
# =============================================================================

# RULE 1: HPA must have required labels
deny contains {
  "msg": sprintf("HPA '%v' missing required label: %v", [input.metadata.name, label]),
  "details": {
    "rule": "required_labels",
    "field": "metadata.labels",
    "missing_label": label,
    "recommendation": sprintf("Add label '%v' to HPA metadata", [label])
  }
} if {
  input.kind == "HorizontalPodAutoscaler"
  required_labels := {"app","environment"}
  some label in required_labels
  not input.metadata.labels[label]
}

# RULE 2: HPA must specify scaleTargetRef
deny contains {
  "msg": sprintf("HPA '%v' must specify scaleTargetRef", [input.metadata.name]),
  "details": {
    "rule": "scale_target_required",
    "field": "spec.scaleTargetRef",
    "recommendation": "Specify the target resource to scale (Deployment, ReplicaSet, etc.)"
  }
} if {
  input.kind == "HorizontalPodAutoscaler"
  not input.spec.scaleTargetRef
}

# RULE 3: HPA must have valid replica bounds
warn contains {
  "msg": sprintf("HPA '%v' minReplicas (%v) must be less than maxReplicas (%v)", [input.metadata.name, input.spec.minReplicas, input.spec.maxReplicas]),
  "details": {
    "rule": "valid_replica_bounds",
    "field": "spec.minReplicas/maxReplicas",
    "min_replicas": input.spec.minReplicas,
    "max_replicas": input.spec.maxReplicas,
    "recommendation": "Ensure minReplicas is less than maxReplicas"
  }
} if {
  input.kind == "HorizontalPodAutoscaler"
  input.spec.minReplicas >= input.spec.maxReplicas
}

# RULE 4: HPA should have reasonable minimum replicas
warn contains {
  "msg": sprintf("HPA '%v' has very low minReplicas (%v) which may cause availability issues", [input.metadata.name, input.spec.minReplicas]),
  "details": {
    "rule": "minimum_replicas_warning",
    "field": "spec.minReplicas",
    "current_value": input.spec.minReplicas,
    "recommendation": "Consider setting minReplicas to at least 2 for production workloads"
  }
} if {
  input.kind == "HorizontalPodAutoscaler"
  input.spec.minReplicas < 2
  input.metadata.labels.environment in {"production", "prod"}
}

# RULE 5: HPA should have reasonable maximum replicas
warn contains {
  "msg": sprintf("HPA '%v' has very high maxReplicas (%v) which may cause resource exhaustion", [input.metadata.name, input.spec.maxReplicas]),
  "details": {
    "rule": "maximum_replicas_warning",
    "field": "spec.maxReplicas",
    "current_value": input.spec.maxReplicas,
    "recommendation": "Consider if such high replica count is necessary and ensure cluster has sufficient resources"
  }
} if {
  input.kind == "HorizontalPodAutoscaler"
  input.spec.maxReplicas > 100
}

# RULE 6: HPA should have metrics defined
deny contains {
  "msg": sprintf("HPA '%v' must have metrics defined for autoscaling", [input.metadata.name]),
  "details": {
    "rule": "metrics_required",
    "field": "spec.metrics",
    "recommendation": "Define CPU, memory, or custom metrics for autoscaling decisions"
  }
} if {
  input.kind == "HorizontalPodAutoscaler"
  input.apiVersion != "autoscaling/v1"  # v1 uses targetCPUUtilizationPercentage
  not input.spec.metrics
}

# RULE 7: HPA should not rely solely on CPU metrics
warn contains {
  "msg": sprintf("HPA '%v' relies only on CPU metrics, consider adding memory or custom metrics", [input.metadata.name]),
  "details": {
    "rule": "multiple_metrics_recommended",
    "field": "spec.metrics",
    "current_metrics": [metric.type | some metric in input.spec.metrics],
    "recommendation": "Add memory utilization or custom metrics for more robust scaling decisions"
  }
} if {
  input.kind == "HorizontalPodAutoscaler"
  input.spec.metrics
  count(input.spec.metrics) == 1
  input.spec.metrics[0].type == "Resource"
  input.spec.metrics[0].resource.name == "cpu"
}

# RULE 8: HPA CPU utilization should be reasonable
warn contains {
  "msg": sprintf("HPA '%v' has very high CPU target (%v%%), may cause performance issues", [input.metadata.name, target_value]),
  "details": {
    "rule": "cpu_target_warning",
    "field": "spec.metrics",
    "target_value": target_value,
    "recommendation": "Consider lowering CPU target to 70-80% for better performance"
  }
} if {
  input.kind == "HorizontalPodAutoscaler"
  some metric in input.spec.metrics
  metric.type == "Resource"
  metric.resource.name == "cpu"
  target_value := get_cpu_target_value(metric)
  target_value > 85
}

# RULE 8a: HPA should use averageUtilization for resource metrics
warn contains {
  "msg": sprintf("HPA '%v' should use averageUtilization instead of averageValue for %v metrics", [input.metadata.name, metric.resource.name]),
  "details": {
    "rule": "use_average_utilization",
    "field": "spec.metrics",
    "resource": metric.resource.name,
    "current_target_type": metric.resource.target.type,
    "recommendation": "Use averageUtilization (percentage of requests) instead of averageValue for better scaling based on resource requests"
  }
} if {
  input.kind == "HorizontalPodAutoscaler"
  some metric in input.spec.metrics
  metric.type == "Resource"
  metric.resource.name in {"cpu", "memory"}
  metric.resource.target.type == "AverageValue"
}

# RULE 8b: HPA CPU utilization should be based on deployment resource requests
warn contains {
  "msg": sprintf("HPA '%v' CPU target (%v%%) should be validated against deployment resource requests", [input.metadata.name, target_value]),
  "details": {
    "rule": "cpu_target_vs_requests",
    "field": "spec.metrics",
    "target_value": target_value,
    "target_deployment": input.spec.scaleTargetRef.name,
    "recommendation": sprintf("Ensure deployment '%v' has appropriate CPU requests. HPA target of %v%% will trigger scaling when pods use %v%% of their requested CPU", [input.spec.scaleTargetRef.name, target_value, target_value])
  }
} if {
  input.kind == "HorizontalPodAutoscaler"
  some metric in input.spec.metrics
  metric.type == "Resource"
  metric.resource.name == "cpu"
  metric.resource.target.type == "Utilization"
  target_value := metric.resource.target.averageUtilization
  input.spec.scaleTargetRef.kind == "Deployment"
}

# RULE 8c: HPA memory utilization should be based on deployment resource requests
warn contains {
  "msg": sprintf("HPA '%v' memory target (%v%%) should be validated against deployment resource requests", [input.metadata.name, target_value]),
  "details": {
    "rule": "memory_target_vs_requests",
    "field": "spec.metrics",
    "target_value": target_value,
    "target_deployment": input.spec.scaleTargetRef.name,
    "recommendation": sprintf("Ensure deployment '%v' has appropriate memory requests. HPA target of %v%% will trigger scaling when pods use %v%% of their requested memory", [input.spec.scaleTargetRef.name, target_value, target_value])
  }
} if {
  input.kind == "HorizontalPodAutoscaler"
  some metric in input.spec.metrics
  metric.type == "Resource"
  metric.resource.name == "memory"
  metric.resource.target.type == "Utilization"
  target_value := metric.resource.target.averageUtilization
  input.spec.scaleTargetRef.kind == "Deployment"
}

# RULE 9: HPA should have behavior configuration for production
warn contains {
  "msg": sprintf("HPA '%v' should configure scaling behavior for production workloads", [input.metadata.name]),
  "details": {
    "rule": "scaling_behavior_recommended",
    "field": "spec.behavior",
    "recommendation": "Configure scaleUp and scaleDown behavior to prevent aggressive scaling"
  }
} if {
  input.kind == "HorizontalPodAutoscaler"
  input.metadata.labels.environment in {"production", "prod"}
  input.apiVersion in {"autoscaling/v2", "autoscaling/v2beta2"}
  not input.spec.behavior
}

# RULE 10: HPA target should exist in same namespace
warn contains {
  "msg": sprintf("HPA '%v' should specify target namespace or ensure target exists in same namespace", [input.metadata.name]),
  "details": {
    "rule": "target_namespace_warning",
    "field": "spec.scaleTargetRef",
    "target_name": input.spec.scaleTargetRef.name,
    "recommendation": "Verify that the target resource exists in the same namespace"
  }
} if {
  input.kind == "HorizontalPodAutoscaler"
  input.spec.scaleTargetRef
  not input.spec.scaleTargetRef.namespace
}

# RULE 11: HPA should use supported API versions
warn contains {
  "msg": sprintf("HPA '%v' uses deprecated API version '%v'", [input.metadata.name, input.apiVersion]),
  "details": {
    "rule": "api_version_warning",
    "field": "apiVersion",
    "current_value": input.apiVersion,
    "recommendation": "Use autoscaling/v2 for better features and future compatibility"
  }
} if {
  input.kind == "HorizontalPodAutoscaler"
  input.apiVersion in {"autoscaling/v2beta1", "autoscaling/v2beta2"}
}

# RULE 12: HPA should have appropriate target types
deny contains {
  "msg": sprintf("HPA '%v' has unsupported scaleTargetRef kind '%v'", [input.metadata.name, input.spec.scaleTargetRef.kind]),
  "details": {
    "rule": "supported_target_kinds",
    "field": "spec.scaleTargetRef.kind",
    "current_value": input.spec.scaleTargetRef.kind,
    "supported_kinds": ["Deployment", "ReplicaSet", "StatefulSet"],
    "recommendation": "Use supported target kinds: Deployment, ReplicaSet, or StatefulSet"
  }
} if {
  input.kind == "HorizontalPodAutoscaler"
  input.spec.scaleTargetRef
  not input.spec.scaleTargetRef.kind in {"Deployment", "ReplicaSet", "StatefulSet"}
}

# RULE 13: HPA should have reasonable CPU utilization targets for typical workloads
warn contains {
  "msg": sprintf("HPA '%v' CPU target (%v%%) may be too conservative for typical web applications", [input.metadata.name, target_value]),
  "details": {
    "rule": "cpu_target_optimization",
    "field": "spec.metrics",
    "target_value": target_value,
    "workload_type": "web-application",
    "recommendation": "For web applications, consider CPU targets between 60-80%. Lower targets (40-60%) may cause premature scaling and resource waste"
  }
} if {
  input.kind == "HorizontalPodAutoscaler"
  some metric in input.spec.metrics
  metric.type == "Resource"
  metric.resource.name == "cpu"
  metric.resource.target.type == "Utilization"
  target_value := metric.resource.target.averageUtilization
  target_value < 50
  is_web_application_hpa(input)
}

# RULE 14: HPA should have reasonable memory utilization targets
warn contains {
  "msg": sprintf("HPA '%v' memory target (%v%%) may be too high and cause OOM issues", [input.metadata.name, target_value]),
  "details": {
    "rule": "memory_target_optimization",
    "field": "spec.metrics",
    "target_value": target_value,
    "recommendation": "Memory targets above 85% may cause OOM kills. Consider 70-85% for memory-based scaling"
  }
} if {
  input.kind == "HorizontalPodAutoscaler"
  some metric in input.spec.metrics
  metric.type == "Resource"
  metric.resource.name == "memory"
  metric.resource.target.type == "Utilization"
  target_value := metric.resource.target.averageUtilization
  target_value > 85
}

# =============================================================================
# Helper Functions
# =============================================================================

# Get CPU target value from metric configuration
get_cpu_target_value(metric) := metric.resource.target.averageUtilization if {
  metric.resource.target.averageUtilization
}

get_cpu_target_value(metric) := metric.resource.target.averageValue if {
  metric.resource.target.averageValue
  not metric.resource.target.averageUtilization
}

get_cpu_target_value(metric) := 0 if {
  not metric.resource.target.averageUtilization
  not metric.resource.target.averageValue
}

# Check if HPA is for a web application based on labels or target name
is_web_application_hpa(hpa) if {
  hpa.metadata.labels["app.kubernetes.io/component"] in {"web", "frontend", "api", "backend"}
}

is_web_application_hpa(hpa) if {
  hpa.metadata.labels["component"] in {"web", "frontend", "api", "backend"}
}

is_web_application_hpa(hpa) if {
  web_keywords := {"web", "api", "frontend", "backend", "service"}
  some keyword in web_keywords
  contains(lower(hpa.spec.scaleTargetRef.name), keyword)
}

# =============================================================================
# Summary Functions
# =============================================================================

# Count total violations
hpa_violation_count := count(deny)

# Count total warnings
hpa_warning_count := count(warn)

# Security and reliability score
hpa_reliability_rules := 6  # Critical reliability rules
hpa_reliability_violations := count([violation | some violation in deny; violation.details.rule in {
  "scale_target_required", "valid_replica_bounds", "metrics_required",
  "supported_target_kinds", "required_labels"
}])

hpa_reliability_score := ((hpa_reliability_rules - hpa_reliability_violations) / hpa_reliability_rules) * 100

# Best practices score
hpa_best_practices_rules := 12  # Best practice rules
hpa_best_practices_violations := count([violation |
  some violation in deny;
  violation.details.rule in {
    "minimum_replicas_warning", "maximum_replicas_warning", "multiple_metrics_recommended",
    "cpu_target_warning", "scaling_behavior_recommended", "target_namespace_warning",
    "api_version_warning", "use_average_utilization", "cpu_target_vs_requests",
    "memory_target_vs_requests", "cpu_target_optimization", "memory_target_optimization"
  }
]) + count([violation |
  some violation in warn;
  violation.details.rule in {
    "minimum_replicas_warning", "maximum_replicas_warning", "multiple_metrics_recommended",
    "cpu_target_warning", "scaling_behavior_recommended", "target_namespace_warning",
    "api_version_warning", "use_average_utilization", "cpu_target_vs_requests",
    "memory_target_vs_requests", "cpu_target_optimization", "memory_target_optimization"
  }
])

hpa_best_practices_score := ((hpa_best_practices_rules - hpa_best_practices_violations) / hpa_best_practices_rules) * 100
