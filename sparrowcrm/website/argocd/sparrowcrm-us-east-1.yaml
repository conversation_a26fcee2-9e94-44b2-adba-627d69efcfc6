apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: sparrowcrm-website-us-east-1
  namespace: argocd
spec:
  project: default
  source:
    repoURL: *****************:surveysparrow/sparrow-k8s-manifests.git
    targetRevision: master
    path: sparrowcrm/website/kustomize/overlays/production/us-east-1/sparrowcrm
  destination:
    server: https://7586588113B8058B3D2221321BF3D37F.gr7.us-east-1.eks.amazonaws.com
    namespace: sparrowcrm-website
  ignoreDifferences:
    - group: apps
      jsonPointers:
        - /spec/replicas
      kind: Deployment
      name: sparrowcrm-website-deployment
      namespace: sparrowcrm-website
    - group: apps
      jsonPointers:
        - /spec/replicas
      kind: Deployment
      name: sparrowcrm-cms-deployment
      namespace: sparrowcrm-website
