apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: sparrowcrmstaging
namePrefix: sparrowcrmstaging-
labels:
- pairs:
    layer: sparrowcrmstaging
resources:
- cms
- website
- secrets
- *****************:surveysparrow/crm-staging-config.git//Website/sparrowcrmstaging?ref=master
- ../../../../../../ingress-base/staging/http-us-east-1/
- arm_karpenter.yaml
- service-account.yaml
- namespace.yaml
images:
- name: website
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/sparrowcrmstaging-website
  newTag: project-init_f61b67a_13
patches:
- path: ingress-patch.yaml
