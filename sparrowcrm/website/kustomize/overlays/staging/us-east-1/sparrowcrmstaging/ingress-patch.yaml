apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:220467647414:certificate/78b5fd29-e81c-4ae4-9d92-0251e0a84f12
spec:
  rules:
    - host: "www.sparrowcrmstaging.com"
      http:
        paths:
        - path: /robots.txt
          pathType: Exact
          backend:
            service:
              name: robots-response
              port:
                name: use-annotation
        - path: /admin
          pathType: Prefix
          backend:
            service:
              name: sparrowcrmstaging-cms-service
              port:
                number: 3000
        - path: /
          pathType: Prefix
          backend:
            service:
              name: sparrowcrmstaging-website-service
              port:
                number: 3000