apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sparrowcrmstaging-website
  template:
    metadata:
      labels:
        app: sparrowcrmstaging-website
    spec:
      nodeSelector:
        app: website-arm
        intent: website
      tolerations:
        - key: "app"
          operator: "Equal"
          value: "website"
          effect: "NoSchedule"
      volumes:
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: sparrowcrmstaging-secrets
      serviceAccountName: sparrowcrmstaging-sa
      containers:
      - name: website
        envFrom:
        - configMapRef:
            name: sparrowcrmstaging-configmap
        - secretRef:
            name: sparrowcrmstaging-secrets
