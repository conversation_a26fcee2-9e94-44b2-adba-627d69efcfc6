apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: nodepool
spec:
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 3m
  limits:
    cpu: "500"
    memory: 1000Gi
  template:
    metadata:
      labels:
        app: website-arm
        intent: website
    spec:
      nodeClassRef:
        name: sparrowcrmstaging-nodeclass
        kind: EC2NodeClass
        group: karpenter.k8s.aws

      taints:
        - key: app
          value: website
          effect: NoSchedule
      requirements:
      - key: karpenter.sh/capacity-type
        operator: In
        values:
        - spot
        - on-demand
      - key: node.kubernetes.io/instance-type
        operator: In
        values:
        - t4g.micro
        - t4g.small
        - t4g.medium
        - t4g.large
      - key: topology.kubernetes.io/zone
        operator: In
        values:
        - us-east-1a
      - key: kubernetes.io/os
        operator: In
        values:
        - linux
      - key: kubernetes.io/arch
        operator: In
        values:
        - arm64   
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: nodeclass
spec:
  kubelet:
    podsPerCore: 12
    maxPods: 20
    systemReserved:
        cpu: 100m
        memory: 100Mi
        ephemeral-storage: 1Gi
    kubeReserved:
        cpu: 200m
        memory: 100Mi
        ephemeral-storage: 3Gi
    evictionHard:
        memory.available: 5%
        nodefs.available: 10%
        nodefs.inodesFree: 10%
    evictionSoft:
        memory.available: 500Mi
        nodefs.available: 15%
        nodefs.inodesFree: 15%
    evictionSoftGracePeriod:
        memory.available: 1m
        nodefs.available: 1m30s
        nodefs.inodesFree: 2m
    evictionMaxPodGracePeriod: 60
    imageGCHighThresholdPercent: 85
    imageGCLowThresholdPercent: 80
    cpuCFSQuota: true
  amiFamily: AL2023
  blockDeviceMappings:
    - deviceName: /dev/xvda
      ebs:
        volumeSize: 21Gi
        volumeType: gp3
        iops: 3000
        encrypted: true
        kmsKeyID: "22de5e70-d2e8-4eb9-bc33-b834a71a65e6"
        deleteOnTermination: true
        throughput: 125

  role: KarpenterStagingWebsiteNodeRole
  amiSelectorTerms:
    - id: ami-0c4b3ccc0c3540aee
    - id: ami-0f684c8cf6a29f0d7
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: "sparrowcrm-staging-cluster"
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: "sparrowcrm-staging-cluster"
  tags:
    karpenter.sh/discovery: "sparrowcrm-staging-cluster"
    Environment: "staging"
    KarpenterProvisionerName: "sparrowcrmstaging"
    CreatedBy: "<EMAIL>"
    Team: "SparrowCrmStaging-Website"
    Service: "Website"
    IntentLabel: "app"
    Name: "Karpenter/sparrowcrmstaging"
    
  metadataOptions:
    httpEndpoint: enabled
    httpProtocolIPv6: enabled
    httpPutResponseHopLimit: 3
    httpTokens: required
  userData: |
    echo "Hello world"    

  detailedMonitoring: false

  associatePublicIPAddress: false
