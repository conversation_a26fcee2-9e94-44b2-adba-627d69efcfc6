apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: sparrowcrm-website
namePrefix: sparrowcrm-
labels:
- pairs:
    layer: sparrowcrm
resources:
- cms
- website
- secrets
- *****************:surveysparrow/crm-production-config.git//Website?ref=master
- ../../../../../../ingress-base/production/http-us-east-1/
- arm-karpenter.yaml
- service-account.yaml
- namespace.yaml
images:
- name: website
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/sparrowcrm-website
  newTag: master_d1e3653_71
patches:
- path: ingress-patch.yaml
