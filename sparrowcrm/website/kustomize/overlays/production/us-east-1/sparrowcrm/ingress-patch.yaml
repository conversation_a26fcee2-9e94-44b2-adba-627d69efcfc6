apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:351491341884:certificate/3a505ecd-2e96-46ed-971b-756654f66baa
    alb.ingress.kubernetes.io/load-balancer-name: sparrowcrm-website-alb
    alb.ingress.kubernetes.io/group.name: sparrowcrm-website-ingress-group
spec:
  ingressClassName: my-aws-ingress-class
  defaultBackend:
    service:
      name: website-service-sparrowcrm
      port:
        number: 3000
  rules:
    - http:
        paths:
        - path: /admin
          pathType: Prefix
          backend:
            service:
              name: sparrowcrm-cms-service
              port:
                number: 3000
        - path: /
          pathType: Prefix
          backend:
            service:
              name: sparrowcrm-website-service
              port:
                number: 3000
                