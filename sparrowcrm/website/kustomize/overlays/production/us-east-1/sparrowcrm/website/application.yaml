apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 2
  replicas: 1
  selector:
    matchLabels:
      app: sparrowcrm-website
  template:
    metadata:
      labels:
        app: sparrowcrm-website
    spec:
      nodeSelector:
        app: website-arm
      tolerations:
        - key: "app"
          operator: "Equal"
          value: "website"
          effect: "NoSchedule"
      volumes:
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: sparrowcrm-secrets
      serviceAccountName: sparrowcrm-sa
      containers:
      - name: website
        envFrom:
        - configMapRef:
            name: sparrowcrm-configmap
        - secretRef:
            name: sparrowcrm-secrets
