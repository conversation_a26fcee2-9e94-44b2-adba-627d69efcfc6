apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sparrowcrm-cms
  template:
    metadata:
      labels:
        app: sparrowcrm-cms
    spec:
      tolerations:
        - key: "app"
          operator: "Equal"
          value: "website"
          effect: "NoSchedule"
      nodeSelector:
        app: website-arm
      volumes:
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: sparrowcrm-secrets
      serviceAccountName: sparrowcrm-sa
      containers:
      - name: website
        envFrom:
        - configMapRef:
            name: sparrowcrm-configmap
        - secretRef:
            name: sparrowcrm-secrets
