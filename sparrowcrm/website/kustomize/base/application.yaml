apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment
  labels:
    app: website
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
  selector:
    matchLabels:
      app: website
  template:
    metadata:
      labels:
        app: website
    spec:
      volumes:
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: secrets #Patch this to the secret provider class name in the overlay
      terminationGracePeriodSeconds: 10
      containers:
      - name: website
        image: website
        imagePullPolicy: IfNotPresent
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 5"]
        volumeMounts:
          - name: secrets-store-inline
            readOnly: true
            mountPath: /mnt/secrets
        resources:
          requests:
            cpu: 300m
            memory: 600Mi
          limits:
            cpu: 1000m
            memory: 2000Mi
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 20
          timeoutSeconds: 15
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          timeoutSeconds: 15
          periodSeconds: 60
          failureThreshold: 5
          successThreshold: 1
        ports:
        - containerPort: 3000