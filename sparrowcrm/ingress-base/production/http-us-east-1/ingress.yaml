apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-port: traffic-port
    alb.ingress.kubernetes.io/healthcheck-path: /
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '30'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '10'
    alb.ingress.kubernetes.io/success-codes: '200'
    alb.ingress.kubernetes.io/healthy-threshold-count: '2'
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '2'
    alb.ingress.kubernetes.io/subnets: subnet-0b6ffdec2bb9f7d8f, subnet-0b938e35f524c2f60, subnet-085c365875f9d825f, subnet-0e015c47337ac8581, subnet-084b0039cd803e55f, subnet-0b51c574d30382721
    alb.ingress.kubernetes.io/security-groups: sg-07fc07703e4d88352
    alb.ingress.kubernetes.io/actions.robots-response: '{"type": "fixed-response","fixedResponseConfig": {"contentType": "text/plain","statusCode": "200","messageBody": "User-agent: *\nDisallow: /"}}'
    alb.ingress.kubernetes.io/tags: "CreatedBy=<EMAIL>,Team=sparrowgenie,Service=sparrowgenie-website"
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=1200,deletion_protection.enabled=true,access_logs.s3.enabled=true,access_logs.s3.bucket=sparrowcrm-alb-logs,access_logs.s3.prefix=sparrowcrm-website-alb

spec:
  ingressClassName: my-aws-ingress-class
  rules: [] # Placeholder for rules in the overlays