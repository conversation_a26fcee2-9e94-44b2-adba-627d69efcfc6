# Pre-commit configuration for SurveySparrow K8s manifests repository
# See https://pre-commit.com for more information

repos:
  # Built-in hooks for basic file hygiene
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
        name: Trim Trailing Whitespace
        description: This hook trims trailing whitespace
        args: [--markdown-linebreak-ext=md]
      
      - id: end-of-file-fixer
        name: Fix End of Files
        description: Ensures that a file is either empty or ends with one newline
      
      - id: check-yaml
        name: Check YAML Syntax
        description: This hook checks yaml files for parseable syntax
        args: ['--multi', '--unsafe']
        exclude: |
          (?x)^(
            .*\.sops\.ya?ml$|
            .*\.enc\.ya?ml$|
            .*templates/.*\.ya?ml$
          )$
      
      - id: check-added-large-files
        name: Check for Large Files
        description: Prevent giant files from being committed
        args: ['--maxkb=1000']
      
      - id: check-case-conflict
        name: Check for Case Conflicts
        description: Check for files that would conflict in case-insensitive filesystems
      
      - id: check-merge-conflict
        name: Check for Merge Conflicts
        description: Check for files that contain merge conflict strings
      
      - id: check-executables-have-shebangs
        name: Check Executables Have Shebangs
        description: Ensures that (non-binary) executables have a shebang
      
      - id: check-shebang-scripts-are-executable
        name: Check Shebang Scripts are Executable
        description: Ensures that (non-binary) files with a shebang are executable
      
      - id: mixed-line-ending
        name: Mixed Line Ending
        description: Replaces or checks mixed line endings
        args: ['--fix=lf']

  # YAML linting with yamllint
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.33.0
    hooks:
      - id: yamllint
        name: YAML Lint
        description: This hook runs yamllint
        args: ['-d', 'relaxed']
        files: \.(yaml|yml)$
        exclude: |
          (?x)^(
            .*\.sops\.ya?ml$|
            .*\.enc\.ya?ml$
          )$

  # Dockerfile linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        name: Dockerfile Lint
        description: Runs hadolint to lint Dockerfiles
        files: Dockerfile.*

  # JSON formatting and validation
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: pretty-format-json
        name: Pretty Format JSON
        description: This hook sets a standard for formatting JSON files
        args: ['--autofix', '--indent=2']
        files: \.json$

  # Kubernetes specific validations
  - repo: local
    hooks:
      - id: kustomize-validate
        name: Kustomize Validation
        description: Validate Kustomize configurations
        entry: bash -c 'find . -name "kustomization.yaml" -not -path "./.git/*" | while read file; do echo "Validating $file"; (cd "$(dirname "$file")" && kustomize build . > /dev/null) || exit 1; done'
        language: system
        files: kustomization\.ya?ml$
        pass_filenames: false
      
      - id: kubernetes-validate
        name: Kubernetes YAML Validation
        description: Validate Kubernetes YAML files
        entry: bash -c 'for file in "$@"; do echo "Validating $file"; kubectl --dry-run=client apply -f "$file" > /dev/null 2>&1 || (echo "❌ Invalid Kubernetes YAML: $file" && exit 1); done'
        language: system
        files: |
          (?x)^(
            .*/(argocd|applications?)/.*\.ya?ml$|
            .*/manifests?/.*\.ya?ml$|
            .*/k8s/.*\.ya?ml$
          )$
        exclude: |
          (?x)^(
            .*kustomization\.ya?ml$|
            .*\.sops\.ya?ml$|
            .*\.enc\.ya?ml$|
            .*/templates/.*\.ya?ml$
          )$
      
      - id: argocd-validate
        name: ArgoCD Application Validation
        description: Validate ArgoCD Application manifests
        entry: bash -c 'for file in "$@"; do echo "Validating ArgoCD app: $file"; if command -v argocd >/dev/null 2>&1; then argocd app validate "$file" || exit 1; else echo "⚠️  ArgoCD CLI not found, skipping validation"; fi; done'
        language: system
        files: |
          (?x)^(
            .*/argocd/.*\.ya?ml$|
            .*application.*\.ya?ml$
          )$
        exclude: kustomization\.ya?ml$

  # Security scanning
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        name: Detect Secrets
        description: Detects high entropy strings that are likely to be passwords
        args: ['--baseline', '.secrets.baseline']
        exclude: |
          (?x)^(
            .*\.lock$|
            .*\.log$|
            package-lock\.json$|
            yarn\.lock$
          )$

  # Commit message validation (runs on commit-msg hook)
  - repo: https://github.com/alessandrojcm/commitlint-pre-commit-hook
    rev: v9.8.0
    hooks:
      - id: commitlint
        name: Commitlint
        description: Lint commit messages
        stages: [commit-msg]
        additional_dependencies: ['@commitlint/config-conventional']

  # Shell script linting
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.9.0.6
    hooks:
      - id: shellcheck
        name: Shellcheck
        description: Shell script analysis tool
        files: \.(sh|bash|ksh|zsh)$

  # Markdown linting
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.37.0
    hooks:
      - id: markdownlint
        name: Markdown Lint
        description: Lint Markdown files
        args: ['--fix']
        files: \.md$

# Global configuration
default_stages: [commit, push]
fail_fast: false
minimum_pre_commit_version: '3.0.0'

# CI configuration
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
