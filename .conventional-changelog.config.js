module.exports = {
  preset: 'angular',
  
  // Custom configuration for SurveySparrow K8s manifests
  releaseCommitMessageFormat: 'chore(release): {{currentTag}}',
  
  // Custom writer options
  writerOpts: {
    transform: (commit, context) => {
      let discard = true;
      const issues = [];

      // Map commit types to sections
      commit.notes.forEach(note => {
        note.title = 'BREAKING CHANGES';
        discard = false;
      });

      // Type mapping for changelog sections
      const typeMapping = {
        feat: 'Features',
        fix: 'Bug Fixes',
        perf: 'Performance Improvements',
        revert: 'Reverts',
        docs: 'Documentation',
        style: 'Styles',
        refactor: 'Code Refactoring',
        test: 'Tests',
        build: 'Build System',
        ci: 'Continuous Integration',
        chore: 'Chores',
        security: 'Security',
        config: 'Configuration'
      };

      if (commit.type && typeMapping[commit.type]) {
        commit.type = typeMapping[commit.type];
      } else if (discard) {
        return;
      }

      // Scope formatting
      if (commit.scope === '*') {
        commit.scope = '';
      }

      // Add scope emoji mapping for better visualization
      const scopeEmojis = {
        argocd: '🔄',
        kustomize: '📦',
        deployment: '🚀',
        service: '🔧',
        ingress: '🌐',
        configmap: '⚙️',
        secret: '🔐',
        hpa: '📈',
        pdb: '🛡️',
        monitoring: '📊',
        security: '🔒',
        strapi: '📝',
        sparrowdesk: '🎫',
        sparrowpay: '💳',
        production: '🏭',
        staging: '🧪',
        sandbox: '🏖️'
      };

      if (commit.scope && scopeEmojis[commit.scope]) {
        commit.scope = `${scopeEmojis[commit.scope]} ${commit.scope}`;
      }

      // Extract ticket IDs from commit message
      const ticketRegex = /([A-Z]+-\d+)/g;
      let ticketMatch;
      while ((ticketMatch = ticketRegex.exec(commit.subject || '')) !== null) {
        issues.push({
          id: ticketMatch[1],
          url: `https://surveysparrow.atlassian.net/browse/${ticketMatch[1]}`
        });
      }

      // Process references (closes, fixes, etc.)
      if (typeof commit.hash === 'string') {
        commit.shortHash = commit.hash.substring(0, 7);
      }

      if (typeof commit.subject === 'string') {
        let url = context.repository
          ? `${context.host}/${context.owner}/${context.repository}`
          : context.repoUrl;
        if (url) {
          url = `${url}/commits/${commit.hash}`;
          commit.subject = commit.subject.replace(/#([0-9]+)/g, (_, issue) => {
            issues.push(issue);
            return `[#${issue}](${url}/issues/${issue})`;
          });
        }
        if (context.host) {
          commit.subject = commit.subject.replace(/\B@([a-z0-9](?:-?[a-z0-9/]){0,38})/g, (_, username) => {
            if (username.includes('/')) {
              return `@${username}`;
            }
            return `[@${username}](${context.host}/${username})`;
          });
        }
      }

      // Back-reference handling
      commit.references.forEach(reference => {
        if (reference.action) {
          issues.push(reference.issue);
        }
      });

      commit.references = commit.references.filter(reference => {
        if (issues.indexOf(reference.issue) === -1) {
          return true;
        }
        return false;
      });

      return commit;
    },

    groupBy: 'type',
    commitGroupsSort: 'title',
    commitsSort: ['scope', 'subject'],
    noteGroupsSort: 'title',
    notesSort: 'text'
  },

  // Custom parser options
  parserOpts: {
    headerPattern: /^(\w*)(?:\((.*)\))?: (.*)$/,
    headerCorrespondence: ['type', 'scope', 'subject'],
    noteKeywords: ['BREAKING CHANGE', 'BREAKING CHANGES'],
    revertPattern: /^(?:Revert|revert:)\s"?([\s\S]+?)"?\s*This reverts commit (\w*)\./i,
    revertCorrespondence: ['header', 'hash'],
    warn: function() {},
    mergePattern: null,
    mergeCorrespondence: null
  },

  // Recommended bump configuration
  recommendedBumpOpts: {
    parserOpts: {
      noteKeywords: ['BREAKING CHANGE', 'BREAKING CHANGES']
    },
    whatBump: (commits) => {
      let level = 2;
      let breakings = 0;
      let features = 0;

      commits.forEach(commit => {
        if (commit.notes.length > 0) {
          breakings += commit.notes.length;
          level = 0;
        } else if (commit.type === 'feat' || commit.type === 'feature') {
          features += 1;
          if (level === 2) {
            level = 1;
          }
        }
      });

      if (level === 2) {
        if (features > 0) {
          level = 1;
        }
      }

      return {
        level: level,
        reason: breakings === 1
          ? `There is ${breakings} BREAKING CHANGE and ${features} features`
          : `There are ${breakings} BREAKING CHANGES and ${features} features`
      };
    }
  }
};
