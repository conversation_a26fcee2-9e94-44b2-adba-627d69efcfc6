apiVersion: batch/v1
kind: Job
metadata:
  name: superset-init-db
spec:
  parallelism: 1
  completions: 1
  backoffLimit: 1
  template:
    spec:
      volumes:
        - name: berlin-superset-config
          configMap:
            name: berlin-superset-config
            defaultMode: 420
        - name: berlin-superset-bootstrap-configmap
          configMap:
            name: berlin-superset-bootstrap-configmap
            defaultMode: 420
        - name: berlin-superset-init-configmap
          configMap:
            name: berlin-superset-init-configmap
            defaultMode: 420
      initContainers:
        - name: wait-for-postgres
          image: 396913698328.dkr.ecr.us-east-1.amazonaws.com/sd-berlin:latest-ci
          command:
            - /bin/sh
            - '-c'
            - dockerize -wait "tcp://$DB_HOST:$DB_PORT" -timeout 120s
          envFrom:
            - configMapRef:
                name: berlin-superset-env-configmap
            - secretRef:
                name: berlin-superset-secrets
          imagePullPolicy: IfNotPresent
      containers:
        - name: berlin-superset-init-db
          image: 396913698328.dkr.ecr.us-east-1.amazonaws.com/sd-berlin:latest-ci
          command:
            - /bin/sh
            - '-c'
            - >-
              . /app/pythonpath/superset_bootstrap.sh; .
              /app/pythonpath/superset_init.sh
          envFrom:
            - configMapRef:
                name: berlin-superset-env-configmap
            - secretRef:
                name: berlin-superset-secrets
          volumeMounts:
            - name: berlin-superset-configmap
              readOnly: true
              mountPath: /app/pythonpath/superset_config.py
              subPath: superset_config.py
            - name: berlin-superset-bootstrap-configmap
              readOnly: true
              mountPath: /app/pythonpath/superset_bootstrap.sh
              subPath: superset_bootstrap.sh
            - name: secrets-store-inline
              readOnly: true
              mountPath: /mnt/secrets
            - name: berlin-superset-init-configmap
              readOnly: true
              mountPath: /app/pythonpath
          imagePullPolicy: IfNotPresent
      restartPolicy: Never
      terminationGracePeriodSeconds: 30
      securityContext:
        runAsUser: 0
      nodeSelector:
        app: berlin
      tolerations:
        - key: app
          operator: Equal
          value: berlin-arm
          effect: NoSchedule
