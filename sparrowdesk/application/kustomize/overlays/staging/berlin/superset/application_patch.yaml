apiVersion: apps/v1
kind: Deployment
metadata:
  name: superset-application
  labels:
    app: superset
spec:
  selector:
    matchLabels:
      app: superset
  template:
    spec:
      volumes:
        - name: berlin-superset-configmap
          configMap:
            name: berlin-superset-configmap
            defaultMode: 420
        - name: berlin-superset-bootstrap-configmap
          configMap:
            name: berlin-superset-bootstrap-configmap
            defaultMode: 420
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: berlin-superset-secrets
        - name: superset-config
          configMap:
            name: superset-config
            defaultMode: 420
      containers:
        - name: superset-app
          ports:
            - name: http
              containerPort: 8088
              protocol: TCP
          envFrom:
            - configMapRef:
                name: berlin-superset-env-configmap
            - secretRef:
                name: berlin-superset-secrets
          env:
            - name: SUPERSET_PORT
              value: '8088'
          volumeMounts:
            - name: berlin-superset-configmap
              readOnly: true
              mountPath: /app/pythonpath/superset_config.py
              subPath: superset_config.py
            - name: berlin-superset-bootstrap-configmap
              readOnly: true
              mountPath: /app/pythonpath/superset_bootstrap.sh
              subPath: superset_bootstrap.sh
            - name: secrets-store-inline
              readOnly: true
              mountPath: /mnt/secrets
