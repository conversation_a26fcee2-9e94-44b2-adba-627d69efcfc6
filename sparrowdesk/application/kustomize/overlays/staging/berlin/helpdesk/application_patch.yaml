apiVersion: apps/v1
kind: Deployment
metadata:
  name: helpdesk-application
spec:
  template:
    spec:
      volumes:
      - name: app-config
        configMap:
          name: berlin-helpdesk-configmap
          items:
          - key: staging.json
            path: staging.json
          defaultMode: 420
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: berlin-helpdesk-secrets
      containers:
      - name: helpdesk-app
        envFrom:
        - configMapRef:
            name: berlin-helpdesk-configmap
        - secretRef:
            name: berlin-helpdesk-secrets
        env:
        - name: VERSION
          value: cd0c62e74d74a34ba9f5a6799eda58e6420e5890
        resources:
          requests:
            cpu: '1'
            memory: 2Gi
          limits:
            memory: 2Gi
        volumeMounts:
        - name: app-config
          mountPath: /app/application/helpdesk-application/config/env/staging.json
          subPath: staging.json
