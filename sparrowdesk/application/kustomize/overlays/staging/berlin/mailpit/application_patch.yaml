apiVersion: apps/v1
kind: Deployment
metadata:
  name: mailpit
  labels:
    application: mailpit
    environment: staging
spec:
  template:
    spec:
      containers:
      - name: mailpit
        securityContext:
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
        resources:
          requests:
            cpu: 400m
            memory: 400Mi
          limits:
            cpu: 800m
            memory: 600Mi
