apiVersion: apps/v1
kind: Deployment
metadata:
  name: chatbot-application
  labels:
    app: chatbot
    environment: staging
spec:
  template:
    spec:
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: berlin-chatbot-secrets
      containers:
        - name: chatbot-app
          envFrom:
            - configMapRef:
                name: berlin-chatbot-configmap
            - secretRef:
                name: berlin-chatbot-secrets
