apiVersion: apps/v1
kind: Deployment
metadata:
  name: public-api
  labels:
    app: public-api
spec:
  template:
    spec:
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: berlin-public-api-secrets
      containers:
      - name: public-api-app
        envFrom:
        - configMapRef:
            name: berlin-public-api-configmap
        - secretRef:
            name: berlin-public-api-secrets
