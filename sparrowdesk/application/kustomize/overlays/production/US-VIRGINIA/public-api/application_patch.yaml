apiVersion: apps/v1
kind: Deployment
metadata:
  name: public-api
  labels:
    app: public-api
    environment: production
spec:
  template:
    spec:
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: sd-us-vi-public-api-secrets
      containers:
        - name: public-api-app
          envFrom:
            - configMapRef:
                name: sd-us-vi-public-api-configmap
            - secretRef:
                name: sd-us-vi-public-api-secrets
