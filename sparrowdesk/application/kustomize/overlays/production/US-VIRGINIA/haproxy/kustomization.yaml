apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - ../../../../base/haproxy
  - ../../../../base/haproxy/high-availability
  - ../../../../../../ingress-base/production/http-us-east-1/

images:
  - name: haproxy-image
    newName: public.ecr.aws/docker/library/haproxy
    newTag: "3.0"

patches:
  - path: application_patch.yaml
  - path: ingress.yaml
  - path: node_selector.yaml
  - path: application_replicas.yaml
