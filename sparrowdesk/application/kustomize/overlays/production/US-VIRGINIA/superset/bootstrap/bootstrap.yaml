apiVersion: batch/v1
kind: Job
metadata:
  name: superset-init-db
  namespace: sparrowdesk
spec:
  parallelism: 1
  completions: 1
  backoffLimit: 1
  template:
    spec:
      volumes:
        - name: sd-us-vi-superset-configmap
          configMap:
            name: sd-us-vi-superset-configmap
            defaultMode: 420
        - name: sd-us-vi-superset-bootstrap-configmap
          configMap:
            name: sd-us-vi-superset-bootstrap-configmap
            defaultMode: 420
        - name: sd-us-vi-superset-init-configmap
          configMap:
            name: sd-us-vi-superset-init-configmap
            defaultMode: 420
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: sd-us-vi-superset-secrets
      # initContainers:
      #   - name: wait-for-postgres
      #     image: 288761754283.dkr.ecr.us-east-1.amazonaws.com/sd-us-vi-superset:superset_staging_b00f1d119_3
      #     command:
      #       - /bin/sh
      #       - '-c'
      #       - dockerize -wait "tcp://$DB_HOST:$DB_PORT" -timeout 120s
      #     envFrom:
      #       - configMapRef:
      #           name: sd-us-vi-superset-env-configmap
      #       - secretRef:
      #           name: sd-us-vi-superset-secrets
      #     imagePullPolicy: IfNotPresent
      containers:
        - name: berlin-superset-init-db
          image: 288761754283.dkr.ecr.us-east-1.amazonaws.com/sd-us-vi-superset:superset_staging_b00f1d119_3
          command:
            - /bin/sh
            - '-c'
            - >-
              . /app/pythonpath/superset_bootstrap.sh; .
              /app/pythonpath/superset_init.sh
          envFrom:
            - configMapRef:
                name: sd-us-vi-superset-env-configmap
            - secretRef:
                name: sd-us-vi-superset-secrets
          volumeMounts:
            - name: sd-us-vi-superset-configmap
              readOnly: true
              mountPath: /app/pythonpath/superset_config.py
              subPath: superset_config.py
            - name: sd-us-vi-superset-bootstrap-configmap
              readOnly: true
              mountPath: /app/pythonpath/superset_bootstrap.sh
              subPath: superset_bootstrap.sh
            - name: secrets-store-inline
              readOnly: true
              mountPath: /mnt/secrets
            - name: sd-us-vi-superset-init-configmap
              readOnly: true
              mountPath: /app/pythonpath/superset_init.sh
              subPath: superset_init.sh
          imagePullPolicy: IfNotPresent
      restartPolicy: Never
      terminationGracePeriodSeconds: 30
      securityContext:
        runAsUser: 0
      serviceAccountName: sd-us-vi-superset-sa
      nodeSelector:
        app: superset-arm
      tolerations:
        - key: app
          operator: Equal
          value: superset-app
          effect: NoSchedule
