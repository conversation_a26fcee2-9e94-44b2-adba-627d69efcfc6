apiVersion: apps/v1
kind: Deployment
metadata:
  name: helpdesk-application
  labels:
    app: helpdesk
    environment: production
spec:
  selector:
    matchLabels:
      app: helpdesk
  template:
    metadata:
      labels:
        app: helpdesk
        environment: production
    spec:
      nodeSelector:
        app: helpdesk-arm
        intent: app
      tolerations:
      - key: app
        operator: Equal
        value: helpdesk-app
        effect: NoSchedule
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - helpdesk
            topologyKey: kubernetes.io/hostname
      volumes:
      - name: app-config
        configMap:
          name: sd-us-vi-helpdesk-configmap
          items:
          - key: production.json
            path: production.json
          defaultMode: 420
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: sd-us-vi-helpdesk-secrets
      containers:
      - name: helpdesk-app
        envFrom:
        - configMapRef:
            name: sd-us-vi-helpdesk-configmap
        - secretRef:
            name: sd-us-vi-helpdesk-secrets
        env:
        - name: VERSION
          value: 2d559ed4dfa0cb448c8afa80135a2a813f7cdded
        resources:
          requests:
            cpu: 500m
            memory: 600Mi
          limits:
            memory: 600Mi
        volumeMounts:
        - name: app-config
          mountPath: /app/application/helpdesk-application/config/env/production.json
          subPath: production.json
