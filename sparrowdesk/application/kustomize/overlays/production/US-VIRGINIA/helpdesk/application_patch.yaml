apiVersion: apps/v1
kind: Deployment
metadata:
  name: helpdesk-application
  labels:
    app: helpdesk
    environment: production
spec:
  template:
    spec:
      volumes:
      - name: app-config
        configMap:
          name: sd-us-vi-helpdesk-configmap
          items:
          - key: production.json
            path: production.json
          defaultMode: 420
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: sd-us-vi-helpdesk-secrets
      containers:
      - name: helpdesk-app
        envFrom:
        - configMapRef:
            name: sd-us-vi-helpdesk-configmap
        - secretRef:
            name: sd-us-vi-helpdesk-secrets
        env:
        - name: VERSION
          value: 54c14c6caca8c69482b974bb3956d7e85494430b
        resources:
          requests:
            cpu: 1000m
            memory: 2000Mi
          limits:
            cpu: 2000m
            memory: 2000Mi
        volumeMounts:
        - name: app-config
          mountPath: /app/application/helpdesk-application/config/env/production.json
          subPath: production.json
