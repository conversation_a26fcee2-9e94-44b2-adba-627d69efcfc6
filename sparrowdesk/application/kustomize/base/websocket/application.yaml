apiVersion: apps/v1
kind: Deployment
metadata:
  name: websocket-application
  labels:
    app: websocket
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: websocket
  template:
    metadata:
      labels:
        app: websocket
    spec:
      terminationGracePeriodSeconds: 30
      containers:
      - name: websocket-app
        image: websocket:app
        imagePullPolicy: IfNotPresent
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 5"]
        resources:
          requests:
            cpu: 300m
            memory: 600Mi
          limits:
            cpu: 1000m
            memory: 2000Mi
        readinessProbe:
          httpGet:
            path: /
            port: 8800
          initialDelaySeconds: 30
          timeoutSeconds: 15
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 2
        livenessProbe:
          httpGet:
            path: /
            port: 8800
          initialDelaySeconds: 40
          timeoutSeconds: 15
          periodSeconds: 60
          failureThreshold: 5
          successThreshold: 1
        ports:
        - containerPort: 8800