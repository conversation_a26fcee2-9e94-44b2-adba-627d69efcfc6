apiVersion: batch/v1
kind: Job
metadata:
  name: public-api-${MIGRATION_TYPE}-migration
  namespace: ${NAMESPACE}
spec:
  template:
    metadata:
      labels:
        app: public-api
    spec:
      restartPolicy: Never
      containers:
      - name: public-api
        workingDir: /app
        command: ["npm", "run", "public-api-migrate:up"]
        args: ["${MIGRATION_TYPE}"]
        envFrom:
        - configMapRef:
            name: ${PREFIX}-public-api-migration-configmap
        - secretRef:
            name: ${PREFIX}-public-api-secrets
        image: ${IMAGE}:${TAG}
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - mountPath: /mnt/secrets
          name: secrets-store-inline
          readOnly: true
      nodeSelector:
        app: ${APP_NAME}-arm
        intent: app
      serviceAccountName: ${PREFIX}-public-api-sa
      terminationGracePeriodSeconds: 30
      tolerations:
      - effect: NoSchedule
        key: app
        operator: Equal
        value: ${TOLERATION_VALUE}-app
      volumes:
      - csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: ${PREFIX}-public-api-secrets
        name: secrets-store-inline
