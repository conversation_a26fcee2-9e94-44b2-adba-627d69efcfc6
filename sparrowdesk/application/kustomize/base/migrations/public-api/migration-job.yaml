apiVersion: batch/v1
kind: Job
metadata:
  name: public-api-${MIGRATION_TYPE}-migration
  namespace: ${ENV_NAME}
spec:
  template:
    metadata:
      labels:
        app: public-api
    spec:
      restartPolicy: Never
      containers:
      - name: public-api
        workingDir: /app
        command: ["npm", "run", "public-api-migrate:up"]
        args: ["${MIGRATION_TYPE}"]
        envFrom:
        - configMapRef:
            name: ${ENV_NAME}-public-api-migration-configmap
        - secretRef:
            name: ${ENV_NAME}-public-api-secrets
        image: ${IMAGE}:${TAG}
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - mountPath: /mnt/secrets
          name: secrets-store-inline
          readOnly: true
      nodeSelector:
        app: ${ENV_NAME}-arm
        intent: app
      serviceAccountName: ${ENV_NAME}-public-api-sa
      terminationGracePeriodSeconds: 30
      tolerations:
      - effect: NoSchedule
        key: app
        operator: Equal
        value: ${ENV_NAME}-env
      volumes:
      - csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: ${ENV_NAME}-public-api-secrets
        name: secrets-store-inline