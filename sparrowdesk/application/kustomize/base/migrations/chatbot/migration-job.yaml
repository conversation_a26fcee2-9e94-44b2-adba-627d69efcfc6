apiVersion: batch/v1
kind: Job
metadata:
  name: chatbot-${MIGRATION_TYPE}-migration
  namespace: ${ENV_NAME}
spec:
  template:
    metadata:
      labels:
        app: chatbot
    spec:
      restartPolicy: Never
      containers:
      - name: chatbot-app
        workingDir: /app
        command: ["npm", "run", "migrate:up"]
        args: ["${MIGRATION_TYPE}"]
        envFrom:
        - configMapRef:
            name: ${ENV_NAME}-chatbot-migration-configmap
        - secretRef:
            name: ${ENV_NAME}-chatbot-secrets
        image: ${IMAGE}:${TAG}
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - mountPath: /mnt/secrets
          name: secrets-store-inline
          readOnly: true
      nodeSelector:
        app: ${ENV_NAME}-arm
        intent: app
      serviceAccountName: ${ENV_NAME}-chatbot-sa
      terminationGracePeriodSeconds: 30
      tolerations:
      - effect: NoSchedule
        key: app
        operator: Equal
        value: ${ENV_NAME}-env
      volumes:
      - csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: ${ENV_NAME}-chatbot-secrets
        name: secrets-store-inline