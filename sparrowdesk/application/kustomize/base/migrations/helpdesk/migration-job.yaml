apiVersion: batch/v1
kind: Job
metadata:
  name: helpdesk-${MIGRATION_TYPE}-migration
  namespace: ${NAMESPACE}
spec:
  template:
    metadata:
      labels:
        app: helpdesk
    spec:
      restartPolicy: Never
      containers:
        - name: helpdesk
          command: ["npm", "run", "migrate:up"]
          args: ["${MIGRATION_TYPE}"]
          envFrom:
            - secretRef:
                name: ${PREFIX}-helpdesk-secrets
            - configMapRef:
                name: ${PREFIX}-helpdesk-migration-configmap
          image: ${IMAGE}:${TAG}
          imagePullPolicy: Always
          volumeMounts:
            - mountPath: /app/application/helpdesk-application/config/env/${ENV}.json
              name: app-config
              subPath: ${ENV}.json
            - mountPath: /mnt/secrets
              name: secrets-store-inline
              readOnly: true
      nodeSelector:
        app: ${APP_NAME}-arm
        intent: app
      serviceAccountName: ${PREFIX}-helpdesk-sa
      tolerations:
        - effect: NoSchedule
          operator: Equal
          key: app
          value: ${TOLERATION_VALUE}-app
      volumes:
        - configMap:
            defaultMode: 420
            items:
              - key: ${ENV}.json
                path: ${ENV}.json
            name: ${PREFIX}-helpdesk-migration-configmap
          name: app-config
        - csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: ${PREFIX}-helpdesk-secrets
          name: secrets-store-inline
  ttlSecondsAfterFinished: 7200  # Cleanup Jobs after 2 hour
  backoffLimit: 0
