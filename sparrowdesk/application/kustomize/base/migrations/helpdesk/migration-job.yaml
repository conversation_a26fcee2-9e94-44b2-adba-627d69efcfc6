apiVersion: batch/v1
kind: Job
metadata:
  name: helpdesk-${MIGRATION_TYPE}-migration
  namespace: ${ENV_NAME}
spec:
  template:
    metadata:
      labels:
        app: helpdesk
    spec:
      restartPolicy: Never
      containers:
        - name: helpdesk
          command: ["npm", "run", "migrate:up"]
          args: ["${MIGRATION_TYPE}"]
          envFrom:
          - secretRef:
              name: ${ENV_NAME}-helpdesk-secrets
          - configMapRef:
              name: ${ENV_NAME}-helpdesk-migration-configmap
          image: ${IMAGE}:${TAG}
          imagePullPolicy: Always
          volumeMounts:
          - mountPath: /app/application/helpdesk-application/config/env/${ENV}.json
            name: app-config
            subPath: ${ENV}.json
          - mountPath: /mnt/secrets
            name: secrets-store-inline
            readOnly: true
      nodeSelector:
        app: ${ENV_NAME}-arm
        intent: app
      serviceAccountName: ${ENV_NAME}-helpdesk-sa
      tolerations:
      - effect: NoSchedule
        operator: Equal
        key: app
        value: ${ENV_NAME}-env
      volumes:
      - configMap:
          defaultMode: 420
          items:
          - key: ${ENV}.json
            path: ${ENV}.json
          name: ${ENV_NAME}-helpdesk-migration-configmap
        name: app-config
      - csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: ${ENV_NAME}-helpdesk-secrets
        name: secrets-store-inline
      restartPolicy: Never
  ttlSecondsAfterFinished: 7200  # Cleanup Jobs after 2 hour
  backoffLimit: 0