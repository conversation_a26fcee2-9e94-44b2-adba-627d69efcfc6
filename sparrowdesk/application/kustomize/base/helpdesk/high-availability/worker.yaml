apiVersion: apps/v1
kind: Deployment
metadata:
  name: helpdesk-worker
  labels:
    app: helpdesk-worker
spec:
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: helpdesk-worker
  template:
    metadata:
      labels:
        app: helpdesk-worker
    spec:
      volumes:
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: secrets #Patch this to the secret provider class name in the overlay
      terminationGracePeriodSeconds: 30
      serviceAccountName: helpdesk-sa
      containers:
      - name: helpdesk-worker
        image: helpdesk:worker
        imagePullPolicy: IfNotPresent
        args: ["dist/helpdesk-application/index.js"]
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 5"]
        volumeMounts:
        - name: secrets-store-inline
          mountPath: /mnt/secrets
          readOnly: true
        resources:
          requests:
            cpu: 300m
            memory: 600Mi
          limits:
            cpu: 1000m
            memory: 2000Mi
        readinessProbe:
          httpGet:
            path: /status
            port: 8080
          initialDelaySeconds: 30
          timeoutSeconds: 15
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 2
        livenessProbe:
          httpGet:
            path: /status
            port: 8080
          initialDelaySeconds: 40
          timeoutSeconds: 15
          periodSeconds: 60
          failureThreshold: 5
          successThreshold: 1
        ports:
        - containerPort: 8080