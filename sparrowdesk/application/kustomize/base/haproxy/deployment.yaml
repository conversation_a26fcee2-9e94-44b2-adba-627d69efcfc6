apiVersion: apps/v1
kind: Deployment
metadata:
  name: haproxy
  labels:
    app: haproxy
spec:
  selector:
    matchLabels:
      app: haproxy
  template:
    metadata:
      labels:
        app: haproxy
    spec:
      containers:
      - name: haproxy
        image: haproxy-image
        ports:
        - containerPort: 8081
        volumeMounts:
        - name: haproxy-config
          mountPath: /usr/local/etc/haproxy/
        livenessProbe:
          httpGet:
            path: /haproxy/monitor
            port: 8081
          initialDelaySeconds: 20
          timeoutSeconds: 50
          periodSeconds: 60
          failureThreshold: 5
          successThreshold: 1
        readinessProbe:
          httpGet:
            path: /haproxy/monitor
            port: 8081
          initialDelaySeconds: 30
          timeoutSeconds: 50
          periodSeconds: 60
          failureThreshold: 5
          successThreshold: 1
      volumes:
      - name: haproxy-config
        configMap:
          name: haproxy-config