apiVersion: apps/v1
kind: Deployment
metadata:
  name: public-api
  labels:
    app: public-api
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: public-api
  template:
    metadata:
      labels:
        app: public-api
    spec:
      volumes:
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: secrets #Patch this to the secret provider class name in the overlay
      terminationGracePeriodSeconds: 30
      serviceAccountName: public-api-sa
      containers:
      - name: public-api-app
        image: public-api:app
        imagePullPolicy: IfNotPresent
        args: ["dist/public-api/index.js"]
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 5"]
        volumeMounts:
        - name: secrets-store-inline
          mountPath: /mnt/secrets
          readOnly: true
        resources:
          requests:
            cpu: 300m
            memory: 600Mi
          limits:
            cpu: 1000m
            memory: 2000Mi
        readinessProbe:
          httpGet:
            path: /v1/check/health
            port: 8002
          initialDelaySeconds: 30
          timeoutSeconds: 15
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 2
        livenessProbe:
          httpGet:
            path: /v1/check/health
            port: 8002
          initialDelaySeconds: 40
          timeoutSeconds: 15
          periodSeconds: 60
          failureThreshold: 5
          successThreshold: 1
        ports:
        - containerPort: 8002