apiVersion: apps/v1
kind: Deployment
metadata:
  name: superset-application
  labels:
    app: superset
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: superset
  template:
    metadata:
      labels:
        app: superset
    spec:
      volumes:
      - name: superset-config
        configMap:
          name: superset-config
          defaultMode: 420
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: secrets # Patch this to the secret provider class name in the overlay
      terminationGracePeriodSeconds: 30
      serviceAccountName: superset-sa
      securityContext:
        runAsUser: 1000
        runAsGroup: 3000
      containers:
      - name: superset-app
        image: superset:app
        imagePullPolicy: IfNotPresent
        command:
        - /bin/sh
        - '-c'
        - . /app/pythonpath/superset_bootstrap.sh; /usr/bin/run-server.sh
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 5"]
        env:
        - name: SUPERSET_PORT
          value: '8088'
        envFrom:
        - configMapRef:
            name: placeholder
        volumeMounts:
        - name: secrets-store-inline
          mountPath: /mnt/secrets
          readOnly: true
        resources:
          limits:
            cpu: '1'
            memory: 2000Mi
          requests:
            cpu: 300m
            memory: 500Mi
        livenessProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 85
          timeoutSeconds: 10
          periodSeconds: 20
          successThreshold: 1
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 65
          timeoutSeconds: 10
          periodSeconds: 20
          successThreshold: 1
          failureThreshold: 5
        securityContext:
          allowPrivilegeEscalation: false
        ports:
        - name: http
          containerPort: 8088
          protocol: TCP
