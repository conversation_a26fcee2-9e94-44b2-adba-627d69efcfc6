apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/certificate-arn: >-
      arn:aws:acm:us-east-1:396913698328:certificate/73bf3ab3-a4fe-4d03-b9b4-1a83c7d4e01a
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/listen-ports: >-
      [{"HTTP": 80}, {"HTTPS":443}]
    alb.ingress.kubernetes.io/actions.ssl-redirect: >-
      {"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-path: haproxy/monitor
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '30'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '10'
    alb.ingress.kubernetes.io/success-codes: '200'
    alb.ingress.kubernetes.io/healthy-threshold-count: '2'
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '2'
    alb.ingress.kubernetes.io/subnets: >-
      subnet-01dae3b0c9f7ed303, subnet-00db01762aed50105, subnet-000fc0658dcd63fa3
    alb.ingress.kubernetes.io/security-groups: sg-0b33b065564b8eae6
    alb.ingress.kubernetes.io/load-balancer-name: sd-common-staging-alb
    alb.ingress.kubernetes.io/group.name: sd-common-staging-ingress-group
    alb.ingress.kubernetes.io/load-balancer-attributes: >-
      deletion_protection.enabled=true,idle_timeout.timeout_seconds=1200
    alb.ingress.kubernetes.io/actions.robots-response: >-
      {"type": "fixed-response","fixedResponseConfig":{"contentType": "text/plain",
      "statusCode": "200","messageBody": "User-agent:*\nDisallow: /"}}
spec:
  ingressClassName: my-aws-ingress-class
  rules: []
