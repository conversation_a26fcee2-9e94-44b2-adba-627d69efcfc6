prometheus:
  enabled: true
  prometheusSpec:
    nodeSelector:
      intent: "monitoring"
      app: "prometheus-arm"
    tolerations:
      - key: "app"
        operator: "Equal"
        value: "prometheus-app"
        effect: "NoSchedule"
    maximumStartupDurationSeconds: 300
    replicas: 1
    podMetadata:
      labels:
        app.kubernetes.io/name: prometheus
        operator.prometheus.io/name: kube-prometheus-stack-prometheus
    remoteWrite:
      - url: https://sparrow-metrics.surveysparrow.com/api/v1/push
        basicAuth:
          username:
            name: mimir-secret
            key: username
          password:
            name: mimir-secret
            key: password
        headers:
          X-Scope-OrgID: sparrowdesk_production
