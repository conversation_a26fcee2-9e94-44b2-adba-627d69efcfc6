apiVersion: apps/v1
kind: Deployment
metadata:
  name: bullboard
  labels:
    app: bullboard
    environment: production
spec:
  template:
    spec:
      containers:
      - name: bullboard
        envFrom:
        - configMapRef:
            name: sparrowdesk-bullboard-configmap
      volumes:
      - name: config-volume
        configMap:
          name: sparrowdesk-bullboard-configmap
          items:
          - key: config
            path: production.json
