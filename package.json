{"name": "sparrow-k8s-manifests", "version": "1.0.0", "description": "Kubernetes manifests for SurveySparrow infrastructure with comprehensive pre-commit workflow", "private": true, "scripts": {"commit": "cz", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "changelog:first": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0", "version:patch": "npm version patch && npm run changelog", "version:minor": "npm version minor && npm run changelog", "version:major": "npm version major && npm run changelog", "lint:yaml": "yam<PERSON>t .", "lint:docker": "find . -name 'Dockerfile*' -exec hadolint {} +", "validate:kustomize": "find . -name 'kustomization.yaml' -execdir kustomize build . \\;", "validate:argocd": "find . -name '*.yaml' -path '*/argocd/*' -exec argocd app validate {} +", "prepare": "husky install", "pre-commit": "pre-commit run --all-files", "install-hooks": "pre-commit install --hook-type commit-msg --hook-type pre-commit"}, "repository": {"type": "git", "url": "*****************:surveysparrow/sparrow-k8s-manifests.git"}, "keywords": ["kubernetes", "k8s", "manifests", "argocd", "kustomize", "surveys<PERSON>row", "infrastructure"], "author": "SurveySparrow DevOps Team", "license": "UNLICENSED", "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "commitizen": "^4.3.0", "conventional-changelog-cli": "^4.1.0", "cz-conventional-changelog": "^3.3.0", "husky": "^8.0.3", "pre-commit": "^1.2.2"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog", "maxHeaderWidth": 100, "maxLineWidth": 100, "defaultType": "", "defaultScope": "", "defaultSubject": "", "defaultBody": "", "defaultIssues": "", "disableScopeLowerCase": false, "disableSubjectLowerCase": false, "format": "{type}{scope}: {subject}", "types": {"feat": {"description": "A new feature", "title": "Features"}, "fix": {"description": "A bug fix", "title": "Bug Fixes"}, "docs": {"description": "Documentation only changes", "title": "Documentation"}, "style": {"description": "Changes that do not affect the meaning of the code", "title": "Styles"}, "refactor": {"description": "A code change that neither fixes a bug nor adds a feature", "title": "Code Refactoring"}, "perf": {"description": "A code change that improves performance", "title": "Performance Improvements"}, "test": {"description": "Adding missing tests or correcting existing tests", "title": "Tests"}, "build": {"description": "Changes that affect the build system or external dependencies", "title": "Builds"}, "ci": {"description": "Changes to our CI configuration files and scripts", "title": "Continuous Integrations"}, "chore": {"description": "Other changes that don't modify src or test files", "title": "Chores"}, "revert": {"description": "Reverts a previous commit", "title": "Reverts"}}}}}