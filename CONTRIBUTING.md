# Contributing to SurveySparrow K8s Manifests

Welcome to the SurveySparrow Kubernetes manifests repository! This guide will help you understand our development workflow, commit conventions, and quality standards.

## 🚀 Quick Start

### Prerequisites

Before contributing, ensure you have the following tools installed:

- **Node.js** (v16 or higher) - for commit tools
- **kubectl** - for Kubernetes validation
- **kustomize** - for Kustomize validation
- **yamllint** - for YAML linting
- **pre-commit** - for pre-commit hooks
- **ArgoCD CLI** (optional) - for ArgoCD validation

### Initial Setup

1. **Clone the repository**:
   ```bash
   <NAME_EMAIL>:surveysparrow/sparrow-k8s-manifests.git
   cd sparrow-k8s-manifests
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Install pre-commit hooks**:
   ```bash
   npm run install-hooks
   # or manually:
   pre-commit install --hook-type commit-msg --hook-type pre-commit
   ```

4. **Verify setup**:
   ```bash
   npm run pre-commit
   ```

## 📝 Commit Message Convention

We use **Conventional Commits** with SurveySparrow-specific enhancements for better changelog generation and release management.

### Commit Message Format

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Commit Types

| Type | Description | Example |
|------|-------------|---------|
| `feat` | ✨ New feature (new service, environment) | `feat(strapi): add production deployment` |
| `fix` | 🐛 Bug fix (configuration, deployment issues) | `fix(argocd): correct application sync policy` |
| `docs` | 📚 Documentation changes | `docs: update deployment guide` |
| `style` | 💎 Formatting changes | `style: fix YAML indentation` |
| `refactor` | 📦 Code refactoring | `refactor(kustomize): reorganize base manifests` |
| `perf` | 🚀 Performance improvements | `perf(hpa): optimize scaling thresholds` |
| `test` | 🚨 Test changes | `test: add validation scripts` |
| `build` | 🛠 Build system changes | `build: update kustomize version` |
| `ci` | ⚙️ CI/CD changes (ArgoCD, pipelines) | `ci(argocd): add new application` |
| `chore` | ♻️ Maintenance tasks | `chore: update image tags` |
| `security` | 🔒 Security improvements | `security(rbac): tighten permissions` |
| `config` | ⚙️ Configuration changes | `config(secrets): update environment variables` |

### Scopes

Common scopes for this repository:

**Infrastructure**: `argocd`, `kustomize`, `deployment`, `service`, `ingress`, `configmap`, `secret`, `hpa`, `pdb`, `rbac`

**Applications**: `strapi`, `sparrowdesk`, `sparrowpay`, `surveysparrow`, `edith`, `chatbot`, `helpdesk`

**Environments**: `production`, `staging`, `sandbox`, `preproduction`

**Regions**: `us-virginia`, `berlin`

### Examples

#### ✅ Good Commit Messages

```bash
# Feature with scope and ticket ID
feat(strapi): add production deployment for US-Virginia region

Implements PROJ-123 requirements:
- Production-ready Strapi deployment
- Auto-scaling configuration
- Security hardening

Closes PROJ-123

# Bug fix with detailed description
fix(argocd): resolve sync policy configuration

The sync policy was causing applications to drift.
Updated to use automated sync with self-heal enabled.

# Configuration change
config(secrets): update database connection strings

Updated connection strings for new RDS instances
in production environment.

# Documentation update
docs: add troubleshooting guide for ArgoCD applications

Added common issues and solutions for ArgoCD
application deployment failures.
```

#### ❌ Bad Commit Messages

```bash
# Too vague
fix: update files

# Missing type
update strapi deployment

# Too long subject line
feat(strapi): add a new production deployment configuration for the Strapi CMS service in the US-Virginia region with auto-scaling and monitoring

# Wrong case
Fix: Update Deployment
```

## 🔧 Development Workflow

### 1. Branch Strategy

Follow the repository's branching strategy:

```
feature-branch → release → preproduction-master → master
```

### 2. Making Changes

1. **Create a feature branch**:
   ```bash
   git checkout -b feat/strapi-production-deployment
   ```

2. **Make your changes** following the guidelines below

3. **Validate your changes**:
   ```bash
   npm run validate:kustomize
   npm run lint:yaml
   ./scripts/validate-k8s.sh
   ```

4. **Commit using commitizen**:
   ```bash
   npm run commit
   # or
   git cz
   ```

5. **Push and create PR**:
   ```bash
   git push origin feat/strapi-production-deployment
   ```

### 3. Quality Standards

#### Kubernetes Manifests

- **Resource Limits**: All deployments must have resource requests and limits
- **Labels**: Use standard labels (`app`, `version`, `environment`)
- **Security**: Follow security best practices (non-root, read-only filesystem)
- **Documentation**: Add comments for complex configurations

#### YAML Formatting

- **Indentation**: 2 spaces
- **Line Length**: Maximum 120 characters
- **Comments**: Use comments to explain complex configurations
- **Consistency**: Follow existing patterns in the repository

#### File Organization

```
platform/
├── service/
│   ├── kustomize/
│   │   ├── base/
│   │   └── overlays/
│   │       ├── production/
│   │       ├── staging/
│   │       └── sandbox/
│   └── argocd/
│       ├── production.yaml
│       ├── staging.yaml
│       └── sandbox.yaml
```

## 🧪 Testing and Validation

### Automated Validation

Pre-commit hooks automatically run:

- YAML syntax validation
- Kubernetes manifest validation
- Kustomize build validation
- Security scanning
- Commit message validation

### Manual Validation

```bash
# Validate all manifests
./scripts/validate-k8s.sh

# Validate specific Kustomize overlay
cd platform/service/kustomize/overlays/production
kustomize build .

# Validate ArgoCD application
argocd app validate argocd/production.yaml

# Run all pre-commit hooks
npm run pre-commit
```

## 📋 Pull Request Guidelines

### PR Title Format

Follow the same convention as commit messages:

```
<type>[optional scope]: <description>
```

Example: `feat(strapi): add production deployment configuration`

### PR Description Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Pre-commit hooks pass
- [ ] Kubernetes manifests validate
- [ ] Kustomize builds successfully
- [ ] ArgoCD applications validate

## Checklist
- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
```

## 🔍 Troubleshooting

### Common Issues

1. **Pre-commit hooks failing**:
   ```bash
   # Reinstall hooks
   npm run install-hooks
   
   # Run specific hook
   pre-commit run yamllint --all-files
   ```

2. **Commit message validation failing**:
   ```bash
   # Use commitizen for guided commits
   npm run commit
   ```

3. **Kustomize validation failing**:
   ```bash
   # Check for syntax errors
   cd path/to/kustomization
   kustomize build . --enable-helm
   ```

### Getting Help

- Check existing issues in the repository
- Review the documentation in `/docs`
- Ask in the team Slack channel
- Contact the DevOps team

## 📚 Additional Resources

- [Conventional Commits](https://www.conventionalcommits.org/)
- [Kubernetes Documentation](https://kubernetes.io/docs/)
- [Kustomize Documentation](https://kustomize.io/)
- [ArgoCD Documentation](https://argo-cd.readthedocs.io/)
- [Pre-commit Documentation](https://pre-commit.com/)

## 🎯 Commit Message Examples

### Real-world Examples

```bash
# Adding new service
feat(strapi): add production deployment with auto-scaling

Implements INFRA-456:
- Production-ready Strapi CMS deployment
- HPA configuration for 3-10 replicas
- Security hardening with non-root user
- AWS Secrets Manager integration

# Fixing configuration issue
fix(argocd): resolve application sync drift in staging

Applications were not syncing properly due to
incorrect targetRevision configuration.

Updated to use 'HEAD' for staging applications.

Fixes INFRA-789

# Security improvement
security(rbac): implement least privilege access

- Removed cluster-admin permissions
- Added specific resource permissions
- Updated service account configurations

# Configuration update
config(secrets): rotate database credentials

Updated database connection strings and credentials
for production environment security compliance.

# Documentation
docs(deployment): add troubleshooting guide

Added common deployment issues and solutions:
- Image pull errors
- Resource quota exceeded
- Network policy conflicts

# Maintenance
chore: update image tags to latest stable versions

Updated all production images to latest stable tags:
- strapi: v4.15.0
- chatbot: v2.3.1
- helpdesk: v1.8.2
```

---

Thank you for contributing to SurveySparrow's infrastructure! 🎉
