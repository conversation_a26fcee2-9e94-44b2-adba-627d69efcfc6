module.exports = {
  extends: ['@commitlint/config-conventional'],
  
  // Custom rules for SurveySparrow K8s manifests repository
  rules: {
    // Type validation - must be one of the specified types
    'type-enum': [
      2,
      'always',
      [
        'feat',     // New feature (new service, environment)
        'fix',      // Bug fix (configuration, deployment issues)
        'docs',     // Documentation changes
        'style',    // Formatting changes
        'refactor', // Code refactoring
        'perf',     // Performance improvements
        'test',     // Test changes
        'build',    // Build system changes
        'ci',       // CI/CD changes (ArgoCD, pipelines)
        'chore',    // Maintenance tasks
        'revert',   // Revert previous commit
        'security', // Security improvements
        'config'    // Configuration changes
      ]
    ],
    
    // Subject validation
    'subject-case': [2, 'never', ['sentence-case', 'start-case', 'pascal-case', 'upper-case']],
    'subject-empty': [2, 'never'],
    'subject-full-stop': [2, 'never', '.'],
    'subject-max-length': [2, 'always', 100],
    'subject-min-length': [2, 'always', 10],
    
    // Header validation
    'header-max-length': [2, 'always', 100],
    'header-min-length': [2, 'always', 15],
    
    // Type validation
    'type-case': [2, 'always', 'lower-case'],
    'type-empty': [2, 'never'],
    
    // Scope validation (optional but if present, must be valid)
    'scope-case': [2, 'always', 'lower-case'],
    'scope-enum': [
      1,
      'always',
      [
        // ArgoCD and GitOps
        'argocd',
        'gitops',
        
        // Kubernetes resources
        'kustomize',
        'deployment',
        'service',
        'ingress',
        'configmap',
        'secret',
        'hpa',
        'pdb',
        'rbac',
        'serviceaccount',
        'networkpolicy',
        
        // Infrastructure
        'monitoring',
        'logging',
        'security',
        'networking',
        'storage',
        'backup',
        'migration',
        'policy',
        'provisioner',
        'nodepool',
        
        // Applications/Services
        'strapi',
        'sparrowdesk',
        'sparrowpay',
        'surveysparrow',
        'edith',
        'chatbot',
        'helpdesk',
        'genie',
        'reputation',
        
        // Environments
        'production',
        'staging',
        'sandbox',
        'preproduction',
        
        // Regions
        'us-virginia',
        'berlin',
        'us-east-1',
        
        // Teams/Squads
        'noforms',
        'sparrowsuite',
        'helpsparrow',
        'officesparrow',
        'seosparrow',
        'servicesparrow',
        'wingssparrow'
      ]
    ],
    
    // Body and footer rules
    'body-leading-blank': [1, 'always'],
    'body-max-line-length': [2, 'always', 100],
    'footer-leading-blank': [1, 'always'],
    'footer-max-line-length': [2, 'always', 100],
    
    // Custom rule for ticket ID validation (optional)
    'ticket-id-format': [1, 'always']
  },
  
  // Custom plugins for additional validation
  plugins: [
    {
      rules: {
        'ticket-id-format': (parsed, when, value) => {
          const { subject, body, footer } = parsed;
          
          // Check if ticket ID is present in subject, body, or footer
          const ticketIdRegex = /(?:^|\s)([A-Z]+-\d+)(?:\s|$)/;
          const hasTicketId = 
            ticketIdRegex.test(subject || '') ||
            ticketIdRegex.test(body || '') ||
            ticketIdRegex.test(footer || '');
          
          // For certain types, ticket ID is recommended but not required
          const recommendedTypes = ['feat', 'fix', 'security', 'config'];
          const requiresTicketId = recommendedTypes.includes(parsed.type);
          
          if (requiresTicketId && !hasTicketId) {
            return [
              false,
              'Ticket ID recommended for this commit type. Format: PROJ-123 or include in body/footer'
            ];
          }
          
          return [true];
        }
      }
    }
  ],
  
  // Custom prompt configuration
  prompt: {
    questions: {
      type: {
        description: "Select the type of change that you're committing:",
        enum: {
          feat: {
            description: '✨ A new feature (new service deployment, new environment)',
            title: 'Features'
          },
          fix: {
            description: '🐛 A bug fix (configuration error, deployment issue)',
            title: 'Bug Fixes'
          },
          docs: {
            description: '📚 Documentation only changes',
            title: 'Documentation'
          },
          style: {
            description: '💎 Changes that do not affect the meaning of the code',
            title: 'Styles'
          },
          refactor: {
            description: '📦 A code change that neither fixes a bug nor adds a feature',
            title: 'Code Refactoring'
          },
          perf: {
            description: '🚀 A code change that improves performance',
            title: 'Performance Improvements'
          },
          test: {
            description: '🚨 Adding missing tests or correcting existing tests',
            title: 'Tests'
          },
          build: {
            description: '🛠 Changes that affect the build system or external dependencies',
            title: 'Builds'
          },
          ci: {
            description: '⚙️ Changes to our CI configuration files and scripts',
            title: 'Continuous Integrations'
          },
          chore: {
            description: '♻️ Other changes that don\'t modify src or test files',
            title: 'Chores'
          },
          revert: {
            description: '🗑 Reverts a previous commit',
            title: 'Reverts'
          },
          security: {
            description: '🔒 Security improvements or fixes',
            title: 'Security'
          },
          config: {
            description: '⚙️ Configuration changes (environment variables, secrets)',
            title: 'Configuration'
          }
        }
      },
      scope: {
        description: 'What is the scope of this change (e.g. component or file name): (press enter to skip)'
      },
      subject: {
        description: 'Write a short, imperative tense description of the change:\n'
      },
      body: {
        description: 'Provide a longer description of the change: (press enter to skip)\n'
      },
      isBreaking: {
        description: 'Are there any breaking changes?'
      },
      breakingBody: {
        description: 'A BREAKING CHANGE commit requires a body. Please enter a longer description of the commit itself:\n'
      },
      breaking: {
        description: 'Describe the breaking changes:\n'
      },
      isIssueAffected: {
        description: 'Does this change affect any open issues or tickets?'
      },
      issuesBody: {
        description: 'If issues are closed, the commit requires a body. Please enter a longer description of the commit itself:\n'
      },
      issues: {
        description: 'Add issue references (e.g. "fix #123", "re #123", "PROJ-456"):\n'
      }
    }
  }
};
