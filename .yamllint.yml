extends: default

yaml-files:
  - '*.yaml'
  - '*.yml'
  - '.yamllint'

rules:
  # 80 chars should be enough, but don't fail if a line is longer
  line-length:
    max: 120
    level: warning
  # Don't require the --- document start marker
  document-start: disable
  # Don't care about the end of files
  document-end: disable
  # Kubernetes uses a lot of truthy values and doesn't usually stick to canonical forms
  truthy:
    allowed-values: ['true', 'false', 'yes', 'no', 'on', 'off']
    check-keys: false
  # Indentation is important in YAML, stick to 2 spaces
  indentation:
    spaces: 2
    indent-sequences: consistent
    check-multi-line-strings: false
  # Allow some flexibility for brackets and arrays
  brackets:
    min-spaces-inside: 0
    max-spaces-inside: 1
    min-spaces-inside-empty: 0
    max-spaces-inside-empty: 0
  # At least one space after : for mapping values
  colons:
    max-spaces-before: 0
    max-spaces-after: 1
  # Be consistent with commas
  commas:
    max-spaces-before: 0
    min-spaces-after: 1
    max-spaces-after: 1
  # Be careful with empty values
  empty-values:
    forbid-in-block-mappings: true
    forbid-in-flow-mappings: true
  # Kubernetes often has empty lines
  empty-lines:
    max: 2
    max-start: 0
    max-end: 0
  # Be consistent with key duplicates in mappings
  key-duplicates: enable
  # Be consistent with key ordering in mappings
  key-ordering: disable
  # Be consistent with the use of new lines
  new-line-at-end-of-file: enable
  new-lines:
    type: unix
  # Kubernetes uses a lot of trailing spaces for formatting
  trailing-spaces: enable
  # Comments should be properly formatted
  comments:
    require-starting-space: true
    min-spaces-from-content: 1
  # Allow special characters in scripts/commands
  comments-indentation: enable
  # Enforce consistent quoting style for strings
  quoted-strings:
    quote-type: single
    required: false
  # Enforce octal values are explicitly prefixed with "0o"
  octal-values:
    forbid-implicit-octal: true
    forbid-explicit-octal: false
  # Enforce anchors naming convention
  hyphens:
    max-spaces-after: 1
